2025-05-20 10:38:33 [http-nio-80-exec-9] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-20 10:38:33 [http-nio-80-exec-9] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 JPA repository interfaces.
2025-05-20 10:38:33 [http-nio-80-exec-9] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-05-20 10:38:33 [http-nio-80-exec-9] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 4 ms. Found 0 Redis repository interfaces.
2025-05-20 11:07:58 [http-nio-80-exec-3] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-05-20 11:07:58 [http-nio-80-exec-3] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 78 ms. Found 0 JPA repository interfaces.
2025-05-20 11:07:58 [http-nio-80-exec-3] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-05-20 11:07:58 [http-nio-80-exec-3] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 3 ms. Found 0 Redis repository interfaces.
2025-05-20 11:19:49 [Thread-15] WARN  org.mariadb.jdbc.message.server.ErrorPacket - Error: 1054-42S22: Unknown column 't.username' in 'field list'
2025-05-20 11:19:49 [Thread-15] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper - SQL Error: 1054, SQLState: 42S22
2025-05-20 11:19:49 [Thread-15] ERROR org.hibernate.engine.jdbc.spi.SqlExceptionHelper - (conn=742) Unknown column 't.username' in 'field list'
2025-05-20 11:19:49 [Thread-15] ERROR com.sys.core.server.BaseImpl - 查询错误错误:{eq={cfgName=<FLOW:8-9-3><APP>ZW_DRG_INDICATOR_DRG}}
org.hibernate.exception.SQLGrammarException: JDBC exception executing SQL [select w1.id,w1.cfg_name,(select max(t.username) from s_users t where t.userid=w1.userid),w1.userid from s_wf_user w1 where w1.cfg_name=? limit ?,?] [(conn=742) Unknown column 't.username' in 'field list'] [n/a]
	at org.hibernate.exception.internal.SQLExceptionTypeDelegate.convert(SQLExceptionTypeDelegate.java:64)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:56)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:94)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.executeQuery(DeferredResultSetAccess.java:257)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.getResultSet(DeferredResultSetAccess.java:163)
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.advanceNext(JdbcValuesResultSetImpl.java:254)
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.processNext(JdbcValuesResultSetImpl.java:134)
	at org.hibernate.sql.results.jdbc.internal.AbstractJdbcValues.next(AbstractJdbcValues.java:19)
	at org.hibernate.sql.results.internal.RowProcessingStateStandardImpl.next(RowProcessingStateStandardImpl.java:66)
	at org.hibernate.sql.results.spi.ListResultsConsumer.consume(ListResultsConsumer.java:198)
	at org.hibernate.sql.results.spi.ListResultsConsumer.consume(ListResultsConsumer.java:33)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.doExecuteQuery(JdbcSelectExecutorStandardImpl.java:361)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.executeQuery(JdbcSelectExecutorStandardImpl.java:168)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.list(JdbcSelectExecutorStandardImpl.java:93)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.list(JdbcSelectExecutor.java:31)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.lambda$new$0(ConcreteSqmSelectQueryPlan.java:110)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.withCacheableSqmInterpretation(ConcreteSqmSelectQueryPlan.java:303)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.performList(ConcreteSqmSelectQueryPlan.java:244)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doList(QuerySqmImpl.java:518)
	at org.hibernate.query.spi.AbstractSelectionQuery.list(AbstractSelectionQuery.java:367)
	at org.hibernate.query.Query.getResultList(Query.java:119)
	at com.sys.core.server.BaseImpl.find(SourceFile:309)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:578)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:751)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703)
	at com.sys.core.server.BaseImpl$$SpringCGLIB$$0.find(<generated>)
	at com.sys.core.zul.GridWnd.onPaging(SourceFile:507)
	at com.sys.core.zul.PageWnd.onPaging(SourceFile:288)
	at com.sys.core.zul.ListWnd.onPaging(SourceFile:240)
	at com.sys.core.zul.GridWnd.onSearch(SourceFile:456)
	at com.sys.core.zul.PageWnd.onSearch(SourceFile:530)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:578)
	at org.zkoss.zk.ui.AbstractComponent.service(AbstractComponent.java:3174)
	at org.zkoss.zk.ui.AbstractComponent.service(AbstractComponent.java:3103)
	at org.zkoss.zk.ui.impl.EventProcessor.process(EventProcessor.java:138)
	at org.zkoss.zk.ui.impl.EventProcessingThreadImpl.process0(EventProcessingThreadImpl.java:545)
	at org.zkoss.zk.ui.impl.EventProcessingThreadImpl.run(EventProcessingThreadImpl.java:470)
Caused by: java.sql.SQLSyntaxErrorException: (conn=742) Unknown column 't.username' in 'field list'
	at org.mariadb.jdbc.export.ExceptionFactory.createException(ExceptionFactory.java:289)
	at org.mariadb.jdbc.export.ExceptionFactory.create(ExceptionFactory.java:378)
	at org.mariadb.jdbc.message.ClientMessage.readPacket(ClientMessage.java:189)
	at org.mariadb.jdbc.client.impl.StandardClient.readPacket(StandardClient.java:1235)
	at org.mariadb.jdbc.client.impl.StandardClient.readResults(StandardClient.java:1174)
	at org.mariadb.jdbc.client.impl.StandardClient.readResponse(StandardClient.java:1093)
	at org.mariadb.jdbc.client.impl.StandardClient.execute(StandardClient.java:1017)
	at org.mariadb.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:101)
	at org.mariadb.jdbc.ClientPreparedStatement.executeQuery(ClientPreparedStatement.java:257)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.executeQuery(DruidPooledPreparedStatement.java:213)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.executeQuery(DeferredResultSetAccess.java:239)
	... 36 common frames omitted
2025-05-20 11:19:49 [http-nio-80-exec-2] ERROR org.zkoss.zk.ui.impl.UiEngineImpl - 
org.springframework.transaction.UnexpectedRollbackException: Transaction silently rolled back because it has been marked as rollback-only
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.processCommit(AbstractPlatformTransactionManager.java:752)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.commit(AbstractPlatformTransactionManager.java:711)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.commitTransactionAfterReturning(TransactionAspectSupport.java:660)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:751)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703)
	at com.sys.core.server.BaseImpl$$SpringCGLIB$$0.find(<generated>)
	at com.sys.core.zul.GridWnd.onPaging(SourceFile:507)
	at com.sys.core.zul.PageWnd.onPaging(SourceFile:288)
	at com.sys.core.zul.ListWnd.onPaging(SourceFile:240)
	at com.sys.core.zul.GridWnd.onSearch(SourceFile:456)
	at com.sys.core.zul.PageWnd.onSearch(SourceFile:530)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:578)
	at org.zkoss.zk.ui.AbstractComponent.service(AbstractComponent.java:3174)
	at org.zkoss.zk.ui.AbstractComponent.service(AbstractComponent.java:3103)
	at org.zkoss.zk.ui.impl.EventProcessor.process(EventProcessor.java:138)
	at org.zkoss.zk.ui.impl.EventProcessingThreadImpl.process0(EventProcessingThreadImpl.java:545)
	at org.zkoss.zk.ui.impl.EventProcessingThreadImpl.run(EventProcessingThreadImpl.java:470)
2025-05-20 11:24:33 [Thread-14] WARN  org.mariadb.jdbc.message.server.ErrorPacket - Error: 1054-42S22: Unknown column 't.username' in 'field list'
2025-05-20 11:24:33 [Thread-14] WARN  org.hibernate.engine.jdbc.spi.SqlExceptionHelper - SQL Error: 1054, SQLState: 42S22
2025-05-20 11:24:33 [Thread-14] ERROR org.hibernate.engine.jdbc.spi.SqlExceptionHelper - (conn=742) Unknown column 't.username' in 'field list'
2025-05-20 11:24:33 [Thread-14] ERROR com.sys.core.server.BaseImpl - 查询错误错误:{eq={cfgName=<FLOW:8-9-3><APP>ZW_DRG_INDICATOR_DRG}, order={asc=[userName]}}
org.hibernate.exception.SQLGrammarException: JDBC exception executing SQL [select w1.id,w1.cfg_name,(select max(t.username) from s_users t where t.userid=w1.userid),w1.userid from s_wf_user w1 where w1.cfg_name=? order by (select max(t.username) from s_users t where t.userid=w1.userid) limit ?,?] [(conn=742) Unknown column 't.username' in 'field list'] [n/a]
	at org.hibernate.exception.internal.SQLExceptionTypeDelegate.convert(SQLExceptionTypeDelegate.java:64)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:56)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:94)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.executeQuery(DeferredResultSetAccess.java:257)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.getResultSet(DeferredResultSetAccess.java:163)
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.advanceNext(JdbcValuesResultSetImpl.java:254)
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.processNext(JdbcValuesResultSetImpl.java:134)
	at org.hibernate.sql.results.jdbc.internal.AbstractJdbcValues.next(AbstractJdbcValues.java:19)
	at org.hibernate.sql.results.internal.RowProcessingStateStandardImpl.next(RowProcessingStateStandardImpl.java:66)
	at org.hibernate.sql.results.spi.ListResultsConsumer.consume(ListResultsConsumer.java:198)
	at org.hibernate.sql.results.spi.ListResultsConsumer.consume(ListResultsConsumer.java:33)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.doExecuteQuery(JdbcSelectExecutorStandardImpl.java:361)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.executeQuery(JdbcSelectExecutorStandardImpl.java:168)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.list(JdbcSelectExecutorStandardImpl.java:93)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.list(JdbcSelectExecutor.java:31)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.lambda$new$0(ConcreteSqmSelectQueryPlan.java:110)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.withCacheableSqmInterpretation(ConcreteSqmSelectQueryPlan.java:303)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.performList(ConcreteSqmSelectQueryPlan.java:244)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doList(QuerySqmImpl.java:518)
	at org.hibernate.query.spi.AbstractSelectionQuery.list(AbstractSelectionQuery.java:367)
	at org.hibernate.query.Query.getResultList(Query.java:119)
	at com.sys.core.server.BaseImpl.find(SourceFile:309)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:578)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:751)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703)
	at com.sys.core.server.BaseImpl$$SpringCGLIB$$0.find(<generated>)
	at com.sys.core.zul.GridWnd.onPaging(SourceFile:507)
	at com.sys.core.zul.PageWnd.onPaging(SourceFile:288)
	at com.sys.core.zul.ListWnd.onPaging(SourceFile:240)
	at com.sys.core.zul.GridWnd.c(SourceFile:425)
	at java.base/java.util.Optional.ifPresentOrElse(Optional.java:198)
	at com.sys.core.zul.GridWnd.b(SourceFile:404)
	at java.base/java.util.LinkedHashMap.forEach(LinkedHashMap.java:729)
	at com.sys.core.zul.GridWnd.onSort(SourceFile:403)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:578)
	at org.zkoss.zk.ui.AbstractComponent.service(AbstractComponent.java:3174)
	at org.zkoss.zk.ui.AbstractComponent.service(AbstractComponent.java:3103)
	at org.zkoss.zk.ui.impl.EventProcessor.process(EventProcessor.java:138)
	at org.zkoss.zk.ui.impl.EventProcessingThreadImpl.process0(EventProcessingThreadImpl.java:545)
	at org.zkoss.zk.ui.impl.EventProcessingThreadImpl.run(EventProcessingThreadImpl.java:470)
Caused by: java.sql.SQLSyntaxErrorException: (conn=742) Unknown column 't.username' in 'field list'
	at org.mariadb.jdbc.export.ExceptionFactory.createException(ExceptionFactory.java:289)
	at org.mariadb.jdbc.export.ExceptionFactory.create(ExceptionFactory.java:378)
	at org.mariadb.jdbc.message.ClientMessage.readPacket(ClientMessage.java:189)
	at org.mariadb.jdbc.client.impl.StandardClient.readPacket(StandardClient.java:1235)
	at org.mariadb.jdbc.client.impl.StandardClient.readResults(StandardClient.java:1174)
	at org.mariadb.jdbc.client.impl.StandardClient.readResponse(StandardClient.java:1093)
	at org.mariadb.jdbc.client.impl.StandardClient.execute(StandardClient.java:1017)
	at org.mariadb.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:101)
	at org.mariadb.jdbc.ClientPreparedStatement.executeQuery(ClientPreparedStatement.java:257)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.executeQuery(DruidPooledPreparedStatement.java:213)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.executeQuery(DeferredResultSetAccess.java:239)
	... 39 common frames omitted
2025-05-20 11:24:34 [http-nio-80-exec-10] ERROR org.zkoss.zk.ui.impl.UiEngineImpl - 
org.springframework.transaction.UnexpectedRollbackException: Transaction silently rolled back because it has been marked as rollback-only
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.processCommit(AbstractPlatformTransactionManager.java:752)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.commit(AbstractPlatformTransactionManager.java:711)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.commitTransactionAfterReturning(TransactionAspectSupport.java:660)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:751)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703)
	at com.sys.core.server.BaseImpl$$SpringCGLIB$$0.find(<generated>)
	at com.sys.core.zul.GridWnd.onPaging(SourceFile:507)
	at com.sys.core.zul.PageWnd.onPaging(SourceFile:288)
	at com.sys.core.zul.ListWnd.onPaging(SourceFile:240)
	at com.sys.core.zul.GridWnd.c(SourceFile:425)
	at java.base/java.util.Optional.ifPresentOrElse(Optional.java:198)
	at com.sys.core.zul.GridWnd.b(SourceFile:404)
	at java.base/java.util.LinkedHashMap.forEach(LinkedHashMap.java:729)
	at com.sys.core.zul.GridWnd.onSort(SourceFile:403)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:578)
	at org.zkoss.zk.ui.AbstractComponent.service(AbstractComponent.java:3174)
	at org.zkoss.zk.ui.AbstractComponent.service(AbstractComponent.java:3103)
	at org.zkoss.zk.ui.impl.EventProcessor.process(EventProcessor.java:138)
	at org.zkoss.zk.ui.impl.EventProcessingThreadImpl.process0(EventProcessingThreadImpl.java:545)
	at org.zkoss.zk.ui.impl.EventProcessingThreadImpl.run(EventProcessingThreadImpl.java:470)
