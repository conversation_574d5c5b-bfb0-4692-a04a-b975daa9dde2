package com.cisdi.entity;

import jakarta.persistence.Column;
import jakarta.persistence.*;
import jakarta.persistence.GeneratedValue;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Parameter;



@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@Entity
@Table(name = "CISDI_TERMINA_MAXNUM")
public class CisdiTerminaMaxnum implements java.io.Serializable {
	private Long id;
	private Long proId;
	private String termina;
	private Integer maxNum;

	@Id
	@GeneratedValue(generator = "CISDI_TERMINA_MAXNUM")
	@GenericGenerator(name = "CISDI_TERMINA_MAXNUM", strategy = "enhanced-table", parameters = {
			@Parameter(name = "table_name", value = "S_TABLES"),
			@Parameter(name = "value_column_name", value = "NEXT_VALUE"),
			@Parameter(name = "segment_column_name", value = "TAB_NAME"),
			@Parameter(name = "segment_value", value = "CISDI_TERMINA_MAXNUM") })
	/**
	 * 主键
	 */
	@Column(name = "ID")
	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	/**
	 * PRO_ID
	 */
	@Column(name = "PRO_ID")
	public Long getProId() {
		return this.proId;
	}

	public void setProId(Long proId) {
		this.proId = proId;
	}

	/**
	 * TERMINA
	 */
	@Column(name = "TERMINA")
	public String getTermina() {
		return this.termina;
	}

	public void setTermina(String termina) {
		this.termina = termina;
	}

	/**
	 * MAX_NUM
	 */
	@Column(name = "MAX_NUM")
	public Integer getMaxNum() {
		return this.maxNum;
	}

	public void setMaxNum(Integer maxNum) {
		this.maxNum = maxNum;
	}

	public boolean equals(Object obj) {
		if (obj instanceof CisdiTerminaMaxnum) {
			CisdiTerminaMaxnum t = (CisdiTerminaMaxnum) obj;
			return id != null ? id.equals(t.id) : false;
		} else {
			return super.equals(obj);
		}
	}

	public int hashCode() {
		return id != null ? id.hashCode() : super.hashCode();
	}
}
