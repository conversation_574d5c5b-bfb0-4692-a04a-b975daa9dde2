package com.cisdi.entity.design;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

@Setter
@Getter
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@Entity
@Table(name = "CISDI_UISTEP_INFO")
public class UistepInfo implements java.io.Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    private String type;
    private String name;
    private String page;
    private Integer sortNo;
    private String iconSclass;
    private Long parentId;
    private Boolean leaf = true;
    private Boolean visible = true;
    private Boolean defaultSel=false;
    public boolean equals(Object obj) {
        return obj instanceof UistepInfo && id != null ? id.equals(((UistepInfo) obj).id) : super.equals(obj);
    }

    public int hashCode() {
        return id != null ? id.hashCode() : super.hashCode();
    }

    @Override
    public String toString() {
        return name;
    }

}
