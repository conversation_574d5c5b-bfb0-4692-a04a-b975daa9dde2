package com.cisdi.server;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.cisdi.entity.*;
import com.cisdi.entity.power.CisdiAutoPowerEqu;
import com.cisdi.entity.power.CisdiPowerPart;
import com.cisdi.entity.power.CisdiPowerRule;
import com.cisdi.entity.power.CisdiPowerScheme;
import com.cisdi.util.CisdiUtils;
import com.sys.common.Message;
import com.sys.common.Utils;
import com.sys.core.api.BaseDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.zkoss.zul.Messagebox;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 0.0.1
 * @description AutoloServer
 * @since 2023/10/26 14:59
 */
@Service
public class AutoIoServer {
    @Autowired
    public BaseDao baseDao;
    @Autowired
    DataServer dataServer;

    private String concat(Object data, List<String> fields) {
        return concat(data, fields, ";");
    }

    private String concat(Object data, List<String> fields, String join) {
        return fields.stream().filter(e -> ObjectUtil.isNotEmpty(BeanUtil.getFieldValue(data, e))).map(e -> String.valueOf(BeanUtil.getFieldValue(data, e))).collect(Collectors.joining(join));
    }

    public String replaceLast(String text, String regex, String replacement) {
        return text.replaceFirst("(?s)" + regex + "(?!.*?" + regex + ")", replacement);
    }

//    private void savePlate(CisdiAutoInter inter, List<Object> otherList, JSONObject tempJson, Map<String, CisdiAutoCab> cabMap) {
//        int index = 1;
//        String key;
//        CisdiAutoPlate plate = null;
//        String plateNum = inter.getEquNum1();
//        List<String> cplates = (List) tempJson.get("cplates");
//        String cabCode = StrUtil.isNotBlank(inter.getEquNum()) && !StrUtil.equals(inter.getCabCode(), inter.getEquNum()) ? inter.getEquNum() : inter.getCabCode();
//
//        if (StrUtil.isNotBlank(inter.getEquName())) {
//            List<String> plateNames = CisdiUtils.plates.stream().filter(e -> ObjectUtil.isNotNull(BeanUtil.getFieldValue(inter, e))).map(e -> (String) BeanUtil.getFieldValue(inter, e)).collect(Collectors.toList());
//            String equName = plateNames.get(plateNames.size() - 1);
//            plateNames.remove(equName);
//            if (plateNames.size() == 1&&tempJson.containsKey("plateEquName")) {
//                equName = tempJson.getStr("plateEquName");
//            } else {
//                for (int i = plateNames.size() - 1; i >= 0; i--) {
//                    equName = replaceLast(equName, plateNames.get(i), "").trim();
//                }
//            }
//            plateNames.add(equName);
//            tempJson.set("plateEquName", equName);
//            for (int i = 0; i < plateNames.size(); i++) {
//                String plateName = plateNames.get(i);
//                plate = null;
//                key = plateNum != null ? cabCode + ">" + equName + ">" + plateNum : cabCode + ">" + equName + ">" + plateName;
//                if (tempJson.containsKey(key)) {
//                    plate = tempJson.get(key, CisdiAutoPlate.class);
//                    if (plateNum != null && !StrUtil.containsAny(plate.getPlateName(), plateName)) {
//                        plate.setPlateName(plate.getPlateName() + " " + plateName);
//                    }
//                    plateNum = null;
//                } else if (!cplates.contains(key)) {
//                    plate = new CisdiAutoPlate();
//                    plate.setSortNo(inter.getSortNo() + (5 - index++));
//                    plate.setId(inter.getId() + ">" + (5 - index));
//                    plate.setPlateNum(plateNum);
//                    plate.setCabCode(cabCode);
//                    plate.setPlateType(StrUtil.equals(cabCode, inter.getCabCode()));
//                    plate.setPlateName(plateName);
//                    plate.setProId(inter.getProId());
//                    plate.setProCode(inter.getCabCode());
//                    plate.setEquNum(inter.getEquNum());
//                    CisdiAutoCab cab = cabMap.get(plate.getPlateType() ? inter.getCabCode() : inter.getEquNum());
//                    plate.setHighCodeDes(cab.getHeightCodeDes());
//                    plate.setHighCode(cab.getHeightCode());
//                    plate.setSeat(cab.getSeat());
//                    plate.setSeatDes(cab.getSeatDes());
//
//                    cplates.add(key);
//                    tempJson.set(key, plate);
//                    if (plateNum != null) {
//                        plate.setPartName(inter.getPartName());
//                    }
//                    plateNum = null;
//                    otherList.add(plate);
//                }
//                if (null != plate) {
//                    if (StrUtil.equals(inter.getSignType(), "DI")) {
//                        plate.addDiNum();
//                    } else if (StrUtil.equals(inter.getSignType(), "DO")) {
//                        plate.addDoNum();
//                    } else if (StrUtil.equals(inter.getSignType(), "AI")) {
//                        plate.addAiNum();
//                    } else if (StrUtil.equals(inter.getSignType(), "Ao")) {
//                        plate.addAoNum();
//                    }
//                }
//            }
//        }
//    }

    //    private <T> T getCisdiModule(String moduleType, String tight) {
//        if (null != moduleType) {
//            String temp = String.format("%s>%s", moduleType, tight);
//            CisdiModule module = MapTimer.get(temp);
//            if (null == module) {
//                module = baseDao.findOne(CisdiModule.class, "module_type='" + moduleType + "' and tight='" + tight + "'");
//                if (null == module) {
//                    if (StrUtil.equals(tight, "是")) {
//                        module = baseDao.findOne(CisdiModule.class, "module_type='" + moduleType + "' and tight='否'");
//                        if (null != module) {
//                            temp = String.format("%s>%s", moduleType, "否");
//                            MapTimer.put(temp, module);
//                        }
//                    }
//                } else {
//                    MapTimer.put(temp, module);
//                }
//            }
//            return MapTimer.get(temp);
//        } else {
//            return null;
//        }
//    }
    private String startWidth(String str, String prefix) {
        if (StrUtil.isNotBlank(str)) {
            if (!str.startsWith(prefix)) {
                return prefix + str;
            }
        }
        return str;
    }

    public List<Object> procInter(List<CisdiAutoInter> inters, Map<String, CisdiAutoModule> moduleMap, CisdiProject project) {
        List<Object> otherList = new ArrayList<>();
        Map<String, Object> param = new HashMap<>();
        param.put("proId", project.getId());
        param.put("rackModel", project.getRackModel());
        //id需要重新更新，不然取不到
        param.put("proId", project.getId());
        String sql = "update cisdi_auto_module set id = uuid() where pro_id=:proId and mod_num is not null AND id <> CONCAT('P',pro_id,'>',cab_code,'>',rack_prefix,rack_num,mod_prefix,mod_num)";
        baseDao.execute(sql, param);
        sql = "update cisdi_auto_module set id = CONCAT('P',pro_id,'>',cab_code,'>',rack_prefix,rack_num,mod_prefix,mod_num) where pro_id=:proId and mod_num is not null AND id <> CONCAT('P',pro_id,'>',cab_code,'>',rack_prefix,rack_num,mod_prefix,mod_num)";
        baseDao.execute(sql, param);

        String hql = "from CisdiAutoCab where proId=" + project.getId();
        List<CisdiAutoCab> cisdiAutoCabs = baseDao.findHql(hql);

        Map<String, CisdiAutoCab> cabMap = cisdiAutoCabs.stream().collect(Collectors.toMap(CisdiAutoCab::getCabCode, e -> e));
        baseDao.execute("delete from cisdi_auto_macros where pro_id=:proId", param);
        baseDao.execute("delete from cisdi_auto_partnr where pro_id=:proId and (part_type='其他完整标识选型' or part_type='灯钮选型')", param);
        AtomicInteger pageNum = new AtomicInteger(0);
        Map<String, Integer> reverseMap = new HashMap<>();
        CisdiModule reModule = dataServer.getCisdiModule("CZXFX", project.getTight());
        AtomicInteger reversePage = new AtomicInteger(30);
        Map<Integer, CisdiAutoMacro> pageMacro = new HashMap<Integer, CisdiAutoMacro>();
        JSONObject tempJson = new JSONObject();
        tempJson.put("cplates", new ArrayList<String>());
        AtomicBoolean saveOk = new AtomicBoolean(true);
        List<String> errors = new ArrayList<>();
        Map<String,CisdiAutoPartnr> partnrMap = new HashMap<>();
        inters.stream().collect(Collectors.groupingBy(CisdiAutoInter::getCabCode, LinkedHashMap::new, Collectors.toList())).forEach((b, n) -> {
            AtomicInteger curPage = new AtomicInteger(35);
            if (saveOk.get())
                n.stream().sorted(Comparator.comparing(CisdiAutoInter::getSortNo)).collect(Collectors.groupingBy(e -> String.format("P%s>%s>%s%s", e.getProId(), e.getCabCode(), e.getRackPrefix(), e.getRackNum()), LinkedHashMap::new, Collectors.toList())).forEach((r, v) -> {//机架
                    tempJson.remove("pmodule");
                    tempJson.remove("pinter");
                    if (saveOk.get())
                        v.stream().collect(Collectors.groupingBy(m -> String.format("%s%s%s", r, m.getModPrefix(), m.getModNum()), LinkedHashMap::new, Collectors.toList())).forEach((o, p) -> {//模块
                            CisdiAutoModule autoModule = moduleMap.get(p.get(0).getModId());
                            CisdiModule smodule = dataServer.getCisdiModule(autoModule.getModType(), project.getTight());
                            pageNum.set(smodule.getChannelNumber() / smodule.getMacroPaging());
                            CisdiAutoModule pmodule = tempJson.getObject("pmodule", CisdiAutoModule.class);
                            if (null != pmodule) {
                                tempJson.remove(pmodule.getModType());
                            }
                            //此时还未赋值
//                            boolean first = StrUtil.contains(p.get(0).getIsolator(), "继电器");//第一个继电器XT1
                            boolean first = false;
                            if (saveOk.get())
                                for (CisdiAutoInter i : p) {//设备
                                    CisdiUtils.startWith(i, Arrays.asList("equNum"), "+", "=", "+");
                                    CisdiScheme scheme = dataServer.getCisdiScheme(i.getSchemeName(), smodule.getTight()==null?"否":smodule.getTight());
                                    if (scheme != null) {
                                        Utils.copyProperties(scheme, i, false);//相同字段为NULL时复制过去
                                    } else {
                                        Message.showError("设备：:R" + i.getRackNum() + ">S" + i.getModNum() + ">" + i.getChannelNum() + ",在系统中未找到方案");
                                        saveOk.set(false);
                                        return;
                                    }

                                    if(StrUtil.isBlank(i.getEquNum()) && StrUtil.containsAny(i.getFullName(),"备用","Spare")){
                                        //当前项目中全是MCC或操作台箱，则备用使用操作台箱的规则
                                        //现场设备数量
                                        long count = inters.stream().filter(e -> StrUtil.isBlank(e.getEquNum()) && !StrUtil.containsAny(i.getFullName(), "备用", "Spare")).count();
                                        if(count == 0 && StrUtil.equals(i.getSignType(), "DI") && ObjectUtil.equals(i.getTermialType(), 2) && StrUtil.equals(project.getMoxFuseTerminal(), "是")){
                                            i.setTermialType(1);
                                        }else if (count > 0 && StrUtil.equals(i.getSignType(), "DI") && ObjectUtil.equals(i.getTermialType(), 2) && StrUtil.equals(project.getFuseTerminal(), "是")){
                                            i.setTermialType(1);
                                        }

                                    }
                                    if (StrUtil.equals(i.getSignType(), "DI") && ObjectUtil.equals(i.getTermialType(), 2) && StrUtil.equals(project.getMoxFuseTerminal(), "是") && StrUtil.containsAny(i.getSchemeName(), "MCC", "操作台箱")) {
                                        i.setTermialType(1);
                                    }

                                    if (StrUtil.equals(i.getSignType(), "DI") && ObjectUtil.equals(i.getTermialType(), 2) && StrUtil.equals(project.getFuseTerminal(), "是") && !StrUtil.containsAny(i.getSchemeName(), "MCC", "操作台箱") && StrUtil.isNotBlank(i.getEquNum()) && !StrUtil.containsAny(i.getFullName(),"备用","Spare")) {
                                        i.setTermialType(1);
                                    }

                                    if (StrUtil.isNotBlank(i.getVirtualPart()) && StrUtil.isNotBlank(i.getEquNum1())) {
                                        if (StrUtil.containsAnyIgnoreCase(i.getEquNum1(), "py", "by")) {
                                            i.setVirtualPart(StrUtil.replace(i.getVirtualPart(), "绿色", "黄色"));
                                        } else if (StrUtil.containsAnyIgnoreCase(i.getEquNum1(), "pr", "br")) {
                                            i.setVirtualPart(StrUtil.replace(i.getVirtualPart(), "绿色", "红色"));
                                        } else if (StrUtil.containsAnyIgnoreCase(i.getEquNum1(), "pw", "bw")) {
                                            i.setVirtualPart(StrUtil.replace(i.getVirtualPart(), "绿色", "白色"));
                                        } else if (StrUtil.containsAnyIgnoreCase(i.getEquNum1(), "bk")) {
                                            i.setVirtualPart(StrUtil.replace(i.getVirtualPart(), "绿色", "黑色"));
                                        }else if (StrUtil.containsAnyIgnoreCase(i.getEquNum1(), "pb","bb")) {
                                            i.setVirtualPart(StrUtil.replace(i.getVirtualPart(), "绿色", "蓝色"));
                                        }
                                        CisdiAutoCab cab = cabMap.get(i.getCabCode());

                                        String part = cab.getHeightCode() + cab.getSeat() + startWidth(i.getEquNum1(), "-");
                                        if (StrUtil.isNotBlank(i.getVirtualPart())) {
                                            String parts[] = i.getVirtualPart().split("#");
                                            if (i.getVirtualPart().contains(";")){
                                                String[] split = i.getVirtualPart().split(";");
                                                int index = -1;
                                                if (StrUtil.contains(i.getEquNum1(),"SA")){
                                                  index = 0;
                                                }else if (StrUtil.contains(i.getEquNum1(),"SW")){
                                                    index = 1;
                                                }else if (StrUtil.contains(i.getEquNum1(),"SR")){
                                                    index = 2;
                                                }else if (StrUtil.contains(i.getEquNum1(),"KS")){
                                                    index = 3;
                                                }
                                                //初始化
                                                if (index >=0 && index < split.length){
                                                    //初始化
                                                    parts = new String[1];
                                                    parts[0] = split[index];
                                                }
                                            }
                                            for (String temp : parts) {
                                                CisdiAutoPartnr partnr = new CisdiAutoPartnr();
                                                partnr.setProId(i.getProId());
                                                partnr.setPartName(part);
                                                partnr.setPartnr(temp);
                                                partnr.setPartNum(1);
                                                partnr.setMdb(i.getId());
                                                partnr.setPartType("灯钮选型");
                                                partnrMap.put(part,partnr);
//                                                otherList.add(partnr);
                                            }
                                        }

                                        if (StrUtil.equals(i.getEquType(), "操作台箱") && StrUtil.containsAny(i.getSchemeName(), "→操作台箱")) {//反写选型
                                            part = cab.getHeightCode() + i.getEquNum().replace(cab.getHeightCode(),"") + startWidth(i.getEquNum1(), "-");
                                            if (StrUtil.isNotBlank(i.getVirtualPart())) {
                                                String parts[] = i.getVirtualPart().split("#");
                                                if (i.getVirtualPart().contains(";")){
                                                    String[] split = i.getVirtualPart().split(";");
                                                    int index = -1;
                                                    if (StrUtil.contains(i.getEquNum1(),"SA")){
                                                        index = 0;
                                                    }else if (StrUtil.contains(i.getEquNum1(),"SW")){
                                                        index = 1;
                                                    }else if (StrUtil.contains(i.getEquNum1(),"SR")){
                                                        index = 2;
                                                    }else if (StrUtil.contains(i.getEquNum1(),"KS")){
                                                        index = 3;
                                                    }

                                                    if (index >=0 && index < split.length){
                                                        //初始化
                                                        parts = new String[1];
                                                        parts[0] = split[index];
                                                    }

                                                }
                                                for (String temp : parts) {
                                                    CisdiAutoPartnr partnr = new CisdiAutoPartnr();
                                                    partnr.setProId(i.getProId());
                                                    partnr.setPartName(part);
                                                    partnr.setPartnr(temp);
                                                    partnr.setPartNum(1);
                                                    partnr.setMdb(i.getId());
                                                    partnr.setPartType("灯钮选型");
                                                    partnrMap.put(part,partnr);
//                                                    otherList.add(partnr);
                                                }
                                            }
                                        }
                                    }
                                    CisdiAutoInter pinter = tempJson.getObject("pinter", CisdiAutoInter.class);
                                    if (StrUtil.isBlank(i.getTermina1Prefix()) && (pinter != null && StrUtil.isNotBlank(pinter.getTermina1Prefix()))) {
                                        i.setTermina1Prefix(pinter.getTermina1Prefix());
                                    }
                                    if (StrUtil.isNotBlank(autoModule.getBfModule()) && StrUtil.isBlank(i.getBfModule()) && null != pinter) {
                                        i.setBfModule(pinter.getBfModule());
                                    }
                                    if (StrUtil.equals(i.getEquType(), "操作台箱") && StrUtil.containsAny(i.getSchemeName(), "→操作台箱")) {//需要反写
                                        String key = String.format("%s>%s>%s", i.getModId(), i.getEquNum(), i.getSignType());
                                        reverseMap.putIfAbsent(key, 0);
                                        CisdiAutoMacro macro = new CisdiAutoMacro();
                                        BeanUtil.copyProperties(i, macro);
                                        macro.setId(macro.getId() + ">0");
                                        macro.setCabCode(i.getEquNum());
                                        macro.setChannel(reverseMap.get(key));
                                        macro.setViewMacro(scheme.getMacroPath());
                                        macro.setMacroType(false);
                                        macro.setBfModule(i.getBfModule());
                                        macro.setChannel(reverseMap.get(key));
                                        if (reverseMap.get(key) % pageNum.get() == 0) {
                                            int page = (int) Math.ceil((reverseMap.get(key) + 1.0) / pageNum.get());
                                            macro.setPage(reversePage.getAndIncrement());
                                            macro.setPageMacro(reModule.getMacroPath() + "\\MY" + i.getSignType() + "^" + page + ".emp");
                                        }

//                                        if (StrUtil.isNotBlank(macro.getPageMacro())){
//                                            //会存在项目属性选错的情况，窗口宏的路径与页宏一致
//                                            String viewMacro = macro.getViewMacro();
//                                            String pageMacroStr = macro.getPageMacro();
//                                            macro.setViewMacro(viewMacro.replace(Arrays.asList(viewMacro.split("\\\\")).get(8),Arrays.asList(pageMacroStr.split("\\\\")).get(8)));
//                                        }
                                        String points[] = smodule.getInterfaceCoordinate().split("#")[0].split(";")[reverseMap.get(key)].split(",");
                                        if (project.getOperationBox() && StrUtil.isNotBlank(smodule.getInterfaceCoordinateTs())) {
                                            points = smodule.getInterfaceCoordinateTs().split("#")[0].split(";")[reverseMap.get(key)].split(",");
                                        }
                                        if (p.stream().map(e -> e.getSignType()).distinct().collect(Collectors.toList()).size() > 1 && StrUtil.contains(key, "DO")) {//两组信号
                                            points = smodule.getInterfaceCoordinate().split("#")[0].split(";")[reverseMap.get(key) + 16].split(",");
                                        }
                                        macro.setX(points[0]);
                                        macro.setY(points[1]);
                                        if (reverseMap.get(key) == smodule.getChannelNumber() - 1) {
                                            reverseMap.put(key, 0);
                                        } else {
                                            reverseMap.put(key, reverseMap.get(key) + 1);
                                        }
                                        otherList.add(macro);
                                    }
                                    CisdiAutoMacro macro = new CisdiAutoMacro();
                                    otherList.add(macro);
                                    BeanUtil.copyProperties(i, macro);

                                    macro.setMacroType(true);

                                    if (autoModule.getJoin() && pageMacro.containsKey(curPage.get())) {//当前需要合并、当前没有页宏，修改上一个的？
                                        CisdiAutoMacro pmacro = pageMacro.get(curPage.get());
                                        String path = smodule.getMacroPath() + "\\Y^" + pmodule.getModType();
                                        if (StrUtil.equals(pmodule.getBottomColor(), "深色")) {
                                            path += "^S";
                                        }
                                        path += ("^" + autoModule.getModType() + "^1");
                                        if (StrUtil.equals(autoModule.getBottomColor(), "深色")) {
                                            path += "^S";
                                        }
                                        path += ".emp";
                                        pmacro.setPageMacro(path);
                                        try {
                                            String points[] = smodule.getInterfaceCoordinate().split("#")[1].split(";")[i.getChannelNum()].split(",");
                                            macro.setX(points[0]);
                                            macro.setY(points[1]);
                                            macro.setPage(curPage.get());
                                        } catch (Exception e) {
//                                            Message.showError(i.getModId() + ":通道号错误");
                                            Message.showError(smodule.getCodeType() + "模块型号与通道数不匹配"+",柜号："+autoModule.getCabCode()+"模块："+StrUtil.concat(true,autoModule.getRackPrefix(),String.valueOf(autoModule.getRackNum()),autoModule.getModPrefix(),String.valueOf(autoModule.getModNum())));
                                        }
                                    } else {
                                        if (i.getChannelNum() % pageNum.get() == 0) {//单开一页
                                            curPage.getAndIncrement();
                                            int page = (int) Math.ceil((i.getChannelNum() + 1.0) / pageNum.get());
                                            String modCode = i.getModCode();
                                            if (StrUtil.isBlank(autoModule.getBfModule()) && StrUtil.containsAny(i.getSignType(), "DI", "DO") && !StrUtil.equals(autoModule.getRackModel(), "ET200SP")) {
                                                if (p.stream().filter(e -> StrUtil.isNotBlank(e.getOutPower1())).count() > 0) {//有外部电源
                                                    modCode = modCode.replace("(MCC)", "");
                                                } else {
                                                    CisdiModule module = dataServer.getCisdiModule(modCode + "(MCC)", project.getTight());
                                                    if (module != null) {
                                                        modCode = modCode + "(MCC)";
                                                    }
                                                }
                                            }
                                            String path = dataServer.getCisdiModule(modCode, project.getTight()).getMacroPath() + "\\Y^" + modCode + "^" + page;
                                            if (StrUtil.equals(autoModule.getBottomColor(), "深色")) {
                                                path += "^S";
                                            }
                                            if (StrUtil.isNotEmpty(autoModule.getBfModule()) && StrUtil.isNotBlank(i.getBfModule())) {// 判断博峰模块当前在多少页
                                                path += "^BF.emp";
                                                String key = i.getModId();
                                                if (!tempJson.containsKey(key)) {
                                                    tempJson.put(key, 1);
                                                } else {
                                                    tempJson.put(key, tempJson.getIntValue(key) + 1);
                                                }
                                                // i.setCpage(tempJson.getInt(key));
                                                macro.setCpage(tempJson.getIntValue(key));

                                                key = String.format("%s>%s", i.getCabCode(), i.getSignType());
                                                if (!tempJson.containsKey(key)) {
                                                    tempJson.put(key, 1);
                                                } else {
                                                    tempJson.put(key, tempJson.getIntValue(key) + 1);
                                                }
                                            } else {
                                                path += ".emp";
                                            }
                                            pageMacro.put(curPage.get(), macro);//只能和新开一页的第一个合并？
                                            macro.setPageMacro(path);

                                        }
                                        String points[];
                                        try {
                                            points = smodule.getInterfaceCoordinate().split("#")[0].split(";")[i.getChannelNum()].split(",");
                                            macro.setX(points[0]);
                                            macro.setY(points[1]);
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                            Message.showError(smodule.getCodeType() + "模块型号与通道数不匹配"+",柜号："+autoModule.getCabCode()+"模块："+StrUtil.concat(true,autoModule.getRackPrefix(),String.valueOf(autoModule.getRackNum()),autoModule.getModPrefix(),String.valueOf(autoModule.getModNum())));
                                            saveOk.set(false);
                                            break;
                                        }
                                        //i.setPage(curPage.get());
                                        //i.setPageMacro(macro.getPageMacro());
                                        macro.setPage(curPage.get());
                                        if (StrUtil.isNotEmpty(i.getBfModule()) && StrUtil.isNotBlank(smodule.getBfInterface())) {//博峰坐标和端子箱前缀
                                            String[] pins = smodule.getBfInterface().split(";");
                                            points = pins[i.getChannelNum()].split(",");
                                            macro.setX(points[0]);
                                            macro.setY(points[1]);

                                            CisdiAutoMatch match = dataServer.getAutoMatch(i.getModCode(), i.getBfModule());
                                            if (null != match) {//这个地方应该可以不用为null判断,在保存之前应该有想在应的判断
                                                i.setBfConnect(match.getBfConnect());
                                                i.setBfRemark(StrUtil.nullToDefault(match.getRemark(), " "));
                                            }
                                        }
                                    }
                                    i.setTerminaAloneCable(StrUtil.equals(scheme.getTerminaAloneCable(), "是"));
                                    i.setPlcTerminaFirst(StrUtil.equals(scheme.getPlcTerminaFirst(), "是"));
                                    i.setEquType(scheme.getEquType());
                                    macro.setViewMacro(scheme.getMacroPath());
//                                    if (StrUtil.isNotBlank(macro.getPageMacro())){
//                                        //会存在项目属性选错的情况，窗口宏的路径与页宏一致
//                                        String viewMacro = macro.getViewMacro();
//                                        String pageMacroStr = macro.getPageMacro();
//                                        macro.setViewMacro(viewMacro.replace(Arrays.asList(viewMacro.split("\\\\")).get(8),Arrays.asList(pageMacroStr.split("\\\\")).get(8)));
//                                    }
                                    //i.setViewMacro(macro.getViewMacro());
                                    if (StrUtil.equals(scheme.getTerminaEnd(), "是")) {// 控制柜内端子短接
                                        i.setTerminaEnd();
                                    }
                                    CisdiPinInfo pin = dataServer.getPinInfo(i.getDetectorType());
                                    i.setShieldDes(pin.getShieldDes());
                                    i.setShield(pin.getShield());
                                    i.setTerminaValue(pin.getTerminaValue());
                                    i.setFeedback(pin.getFeedback());
                                    i.setEquCabSize(pin.getCabSize());// 电缆根数
                                    String vcc = concat(pin, CisdiUtils.vcc);// 电源+
                                    String vss = concat(pin, CisdiUtils.vss);// 电源-
                                    String sign = concat(pin, CisdiUtils.sign);// 信号
                                    String pointDes = concat(pin, CisdiUtils.connectDes);
                                    String point = concat(pin, CisdiUtils.connect);

                                    i.setVcc(vcc);
                                    i.setVss(vss);
                                    i.setSign(sign);
                                    i.setConnectPoint(point);
                                    i.setConnectDes(pointDes);
                                    i.setCbVccSize(getSplitSize(vcc, ";"));
                                    i.setCbVssSize(getSplitSize(vss, ";"));
                                    i.setCbSignSize(getSplitSize(sign, ";"));
                                    macro.setCbSize(i.getCabSize());
                                    List<String> nums = null;
                                    if (StrUtil.isNotBlank(i.getTermina1Num())) {
                                        i.setTermina1();
                                        nums = StrUtil.split(i.getTermina1Num(), ";");
                                        if (StrUtil.containsAnyIgnoreCase(i.getEquType(), "MCC", "操作台箱") && StrUtil.isNotEmpty(i.getEquNum())) {
                                            if (pinter == null || StrUtil.equals(i.getEquNum(), pinter.getEquNum())) {
                                                if (nums.size() == 1) {
                                                    if (StrUtil.isBlank(i.getEquNum1()) && StrUtil.isNotBlank(i.getEquNum2())) {
                                                        i.setPwa2Chanel(1 == i.getCbVccSize() ? "是" : "否");
                                                        i.setPws2Chanel(1 == i.getCbVssSize() ? "是" : "否");
                                                    } else {
                                                        i.setPwa1Chanel(1 == i.getCbVccSize() ? "是" : "否");
                                                        i.setPws1Chanel(1 == i.getCbVssSize() ? "是" : "否");
                                                    }
                                                    i.setCbVccSize(0);
                                                    i.setCbVssSize(0);
                                                }
                                            }
                                            if (i.getCbVccSize() > 0) {
                                                i.setEquVcc(nums.subList(0, i.getCbVccSize()).stream().collect(Collectors.joining(";")));
                                                nums = removeFirst(nums, i.getCbVccSize());
                                            }
                                            if (i.getCbVssSize() > 0 && nums.size() > 0) {
                                                i.setEquVss(nums.subList(0, i.getCbVssSize()).stream().collect(Collectors.joining(";")));
                                                nums = removeFirst(nums, i.getCbVssSize());
                                            }
                                            if (i.getCbSignSize() > 0 && nums.size() > 0) {
                                                i.setEquSign(nums.stream().collect(Collectors.joining(";")));
                                            }
                                        } else {
                                            if (nums.size() != i.getAllSize()) {
                                                errors.add(i.getId());
                                            }
                                            if (i.getCbVccSize() > 0) {
                                                i.setTermina1Vcc(nums.subList(0, i.getCbVccSize()).stream().collect(Collectors.joining(";")));
                                                nums = removeFirst(nums, i.getCbVccSize());
                                            }
                                            if (i.getCbVssSize() > 0 && nums.size() > 0) {
                                                i.setTermina1Vss(nums.subList(0, i.getCbVccSize()).stream().collect(Collectors.joining(";")));
                                                nums = removeFirst(nums, i.getCbVssSize());
                                            }
                                            if (i.getCbSignSize() > 0 && nums.size() > 0) {
                                                i.setTermina1Sign(nums.stream().collect(Collectors.joining(";")));
                                            }
                                        }
                                    }

                                    if (StrUtil.isNotEmpty(i.getTermina2Num())) {
                                        i.setTermina2();
                                        nums = StrUtil.split(i.getTermina2Num(), ";");
                                        if (nums.size() != i.getAllSize()) {
                                            errors.add(i.getId());
                                        }
                                        if (i.getCbVccSize() > 0) {
                                            i.setTermina2Vcc(nums.subList(0, i.getCbVccSize()).stream().collect(Collectors.joining(";")));
                                            nums = removeFirst(nums, i.getCbVccSize());
                                        }
                                        if (i.getCbVssSize() > 0 && nums.size() > 0) {
                                            i.setTermina2Vss(nums.subList(0, i.getCbVccSize()).stream().collect(Collectors.joining(";")));
                                            nums = removeFirst(nums, i.getCbVssSize());
                                        }
                                        if (i.getCbSignSize() > 0 && nums.size() > 0) {
                                            i.setTermina2Sign(nums.stream().collect(Collectors.joining(";")));
                                        }
                                    }
                                    if (StrUtil.isNotBlank(i.getTermina3Num())) {
                                        i.setTermina3();
                                        nums = StrUtil.split(i.getTermina3Num(), ";");
                                        if (nums.size() != i.getAllSize()) {
                                            errors.add(i.getId());
                                        }

                                        if (i.getCbVccSize() > 0) {
                                            i.setTermina3Vcc(nums.subList(0, i.getCbVccSize()).stream().collect(Collectors.joining(";")));
                                            nums = removeFirst(nums, i.getCbVccSize());
                                        }
                                        if (i.getCbVssSize() > 0 && nums.size() > 0) {
                                            i.setTermina3Vss(nums.subList(0, i.getCbVccSize()).stream().collect(Collectors.joining(";")));
                                            nums = removeFirst(nums, i.getCbVssSize());
                                        }
                                        if (i.getCbSignSize() > 0 && nums.size() > 0) {
                                            i.setTermina3Sign(nums.stream().collect(Collectors.joining(";")));
                                        }

                                    }
                                    if (StrUtil.containsAnyIgnoreCase(scheme.getIsolateMode(), "继电器", "隔离器", "放大板")) {
                                        String key = StrUtil.containsAnyIgnoreCase(scheme.getIsolateMode(), "继电器")
                                                ? scheme.getIsolateMode()
                                                : i.getIsolatorCode();
                                        CisdiIsolatorInfo isolator = dataServer.getIsolatorInfo(key);
                                        if (null != isolator) {
                                            String name = scheme.getIsolateMode();
                                            name = name.contains("继电器") ? name.contains("固态继电器")?"固态继电器":"继电器" : name.contains("隔离器") ? "隔离器" : "放大板";
                                            i.setIsolator(name);
                                            i.setEntityPart(isolator.getEntitySelect());
                                            i.setIsolatorCode(isolator.getDetectorType());
                                            i.setIsolatorMacro(isolator.getName());
                                            i.setIsolatorConnect(concat(isolator, CisdiUtils.connect));
                                            i.setIsolatorDesc(concat(isolator, CisdiUtils.connectDes));
                                            boolean lator = StrUtil.isNotBlank(concat(isolator, CisdiUtils.vcc));
                                            lator = lator || StrUtil.isNotBlank(concat(isolator, CisdiUtils.vss));
                                            i.setIsolatorVcc(lator);
                                            if (StrUtil.containsAny(i.getIsolator(), "隔离器")) {
                                                i.setIsolatorNo(String.format("%s%s%s%02d", i.getRackNum(), i.getModNum(), isolator.getMiddleCode(), i.getChannelNum() + 1));
                                                if (pinter != null && StrUtil.equals(pinter.getIsolator(), "隔离器")) {
                                                    if (StrUtil.isBlank(i.getIsolatorVccNo())) {
                                                        i.setIsolatorVccNo(pinter.getIsolatorVccNo());
                                                        i.setIsolatorVssNo(pinter.getIsolatorVssNo());
                                                    }
                                                    if (!StrUtil.equals(pinter.getIsolatorVccNo(),
                                                            i.getIsolatorVccNo())) {
                                                        i.setIsolator(true);
                                                    }
                                                } else if (pinter == null || i.getModNum() != pinter.getModNum().intValue()) {
                                                    i.setIsolator(true);
                                                }
                                            }
                                        } else {
                                            if (saveOk.get()) {
                                                if (StrUtil.isNotBlank(key)) {
                                                    Message.showError(scheme.getIsolateMode() + "(" + key + ")错误:" + i.getId() + "->" + i.getSchemeName());
                                                } else {
                                                    Message.showError("无" + scheme.getIsolateMode() + ":" + i.getId() + "->" + i.getSchemeName());
                                                }
                                                saveOk.set(false);
                                                break;
                                            }
                                        }
                                    }
                                    if (i.getChannelNum() == 0) {
                                        first = StrUtil.equals(i.getIsolator(), "继电器");//第一个继电器XT1
                                    }

                                    if (StrUtil.isBlank(autoModule.getBfModule()) || StrUtil.equals(i.getBfPmacro(), "是") || StrUtil.equals(i.getTerminaValue(), "电源+1信号+1")) {
                                        if (first) {//第一个继电器
                                            if (StrUtil.equals(i.getIsolator(), "继电器")) {
                                                i.setPlcPrefix(String.format("%s%s%s%s%sXT1", i.getRackPrefix(), i.getRackNum(), i.getModPrefix(), i.getModNum(), i.getSignType()));
                                            } else {
                                                i.setPlcPrefix(String.format("%s%s%s%s%sXT2", i.getRackPrefix(), i.getRackNum(), i.getModPrefix(), i.getModNum(), i.getSignType()));
                                            }
                                        } else {
                                            if (StrUtil.equals(i.getIsolator(), "继电器")) {
                                                i.setPlcPrefix(String.format("%s%s%s%s%sXT2", i.getRackPrefix(), i.getRackNum(), i.getModPrefix(), i.getModNum(), i.getSignType()));
                                            } else {
                                                i.setPlcPrefix(String.format("%s%s%s%s%sXT1", i.getRackPrefix(), i.getRackNum(), i.getModPrefix(), i.getModNum(), i.getSignType()));
                                            }
                                        }
                                    }
                                    if (StrUtil.isNotBlank(i.getTermina2()) && StrUtil.isBlank(i.getTermina2Prefix())) {
                                        if (null != pinter && StrUtil.isNotBlank(pinter.getTermina2Prefix())) {
                                            i.setTermina2Prefix(pinter.getTermina2Prefix());
                                        } else {
                                            i.setTermina2Prefix("X1");
                                        }
                                    }
                                    if (StrUtil.isNotBlank(i.getTermina3()) && StrUtil.isBlank(i.getTermina3Prefix())) {
                                        if (null != pinter && StrUtil.isNotBlank(pinter.getTermina3Prefix())) {
                                            i.setTermina3Prefix(pinter.getTermina3Prefix());
                                        } else {
                                            i.setTermina3Prefix("X1");
                                        }
                                    }

                                    tempJson.put("pinter", i);
                                    tempJson.put("pmodule", autoModule);
                                    Utils.defaultValue(i, "", null);
                                }
                        });
                });
        });
        List<CisdiAutoPartnr> partnrList = partnrMap.values().stream().toList();
        otherList.addAll(partnrList);
        List<String> errorList = errors.stream().distinct().collect(Collectors.toList());
        if (!errorList.isEmpty()) {
            StringBuffer buffer = new StringBuffer();
            errorList.stream().forEach(e -> {
                buffer.append(e).append("\r\n");
            });
//            if (Messagebox.NO == Message.showQuestion("端子号填写错误,是否继续:\r\n" + buffer.toString())) {
//                saveOk.set(false);
//            }
        }
        return saveOk.get() ? otherList : null;
    }

    private List<String> removeFirst(List<String> list, int size) {
        if (list.size() > size) {
            list = list.subList(size, list.size());
        } else {
            list = ListUtil.empty();
        }
        return list;
    }

//    private <T> T getEntity(EnumData.TIMER_TYPE type, String key) {
//        if (StrUtil.isBlank(key)) {
//            return null;
//        }
//        String temp = String.format("%s>%s", type.name(), key);
//        if (null == MapTimer.get(temp)) {
//            MapTimer.put(temp, baseDao.find(EnumData.getEntity(type), key));
//        }
//        return MapTimer.get(temp);
//    }

    private int getSplitSize(String str, String split) {
        if (StrUtil.isEmpty(str)) {
            return 0;
        }
        return str.split(split).length;
    }

    /**
     * 电源匹配方案
     *
     * @param powerRuleList
     * @param powerSchemeList
     * @param powerPartList
     * @param equ
     */
    public void matchScheme(List<CisdiPowerRule> powerRuleList, List<CisdiPowerScheme> powerSchemeList, List<CisdiPowerPart> powerPartList, CisdiAutoPowerEqu equ, CisdiProject project) {
        //是否备用
        String isBy;
        if (StrUtil.containsAny(equ.getEquName(), "备用","Spare") && equ.getEquNum() == null) {
            isBy = "是";
        } else {
            isBy = "否";
        }
        if (StrUtil.equals(project.getIoTemPower(), "单独断路器") && StrUtil.contains(equ.getEquName(), "I/O模板电源")) {
            //固定的窗口宏
            equ.setViewMacro("无F(2P-24V)");
            CisdiPowerScheme cisdiPowerScheme = powerSchemeList.stream().filter(p -> StrUtil.equals(p.getSchemeName(), equ.getViewMacro())).findFirst().orElse(null);
            if (cisdiPowerScheme != null) {
                equ.setViewPath(cisdiPowerScheme.getSchemePath());
                equ.setSpace(cisdiPowerScheme.getSpace());
                equ.setVirtualPart(null);
            }

        } else {
            CisdiPowerRule cisdiPowerRule = powerRuleList.stream().filter(p -> StrUtil.equals(isBy, p.getIsBy()) && StrUtil.equals(equ.getSource(), p.getSource()) && ObjectUtil.equals(p.getVoltage(), equ.getVoltage()) && StrUtil.equals(equ.getTerminaPrefix(), p.getHasTermina())).findFirst().orElse(null);
            if (cisdiPowerRule != null) {
                //仪表电源特殊处理
                if (!StrUtil.contains(equ.getViewMacro(), "内部")) {
                    if (StrUtil.equals(equ.getPower1(), "外部") && !StrUtil.contains(equ.getViewMacro(), "外部")) {
                        //加上外部2个字去匹配方案
                        equ.setViewMacro(cisdiPowerRule.getViewMacro() + equ.getPower1());
                    } else {
                        equ.setViewMacro(cisdiPowerRule.getViewMacro());
                    }
                }
                CisdiPowerScheme cisdiPowerScheme = powerSchemeList.stream().filter(p -> StrUtil.equals(p.getSchemeName(), equ.getViewMacro())).findFirst().orElse(null);
                if (cisdiPowerScheme != null) {
                    equ.setViewPath(cisdiPowerScheme.getSchemePath());
                    equ.setSpace(cisdiPowerScheme.getSpace());
                }
                CisdiPowerPart cisdiPowerPart = powerPartList.stream().filter(p -> StrUtil.equals(p.getViewMacro(), equ.getViewMacro()) && ObjectUtil.equals(p.getPower(), equ.getPower())).findFirst().orElse(null);
                if (cisdiPowerPart != null) {
                    equ.setVirtualPart(cisdiPowerPart.getVirtualPart());
                }
            }
        }
    }

}
