package com.cisdi.ui;

import cn.hutool.core.util.StrUtil;
import com.cisdi.entity.CisdiProject;
import com.cisdi.entity.CisdiReportTerminal;
import com.sys.common.Utils;
import com.sys.core.zul.ListWnd;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.event.Events;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 0.0.1
 * @description CisdiBoxSizeListWnd
 * @since 2024/1/16 9:51
 */
public class CisdiBoxSizeListWnd extends ListWnd {
    public void add() {
        Utils.openList("cisdi_report_terminal", getMainData(), true, this);
    }


    @Override
    public void listSure(String listname, Component comp, List<Object> datas) {
        super.listSure(listname, comp, datas);
        CisdiProject project = getMainData();
        baseDao.execute("delete from cisdi_report_terminal where pro_id=" + project.getId());
        if (StrUtil.equals(listname, "cisdi_report_terminal")) {
            List<CisdiReportTerminal> terminalList = datas.stream().map(e -> (CisdiReportTerminal) e).collect(Collectors.toList());
            terminalList.forEach(x -> {
                x.setId(null);
                x.setProId(project.getId());
            });
            baseDao.save(terminalList);
        }
        Events.postEvent("onSearch", this, true);
    }

    public void submit(){
        DesignChildWnd design = (DesignChildWnd) parentWnd;
        design.updateUi(3);
    }
}
