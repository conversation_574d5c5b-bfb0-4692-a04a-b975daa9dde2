package com.cisdi.ui;

import cn.hutool.core.util.StrUtil;
import com.cisdi.entity.CisdiUnrealEntity;
import com.sys.core.zul.ResultWnd;
import io.keikai.model.SBook;
import io.keikai.model.SSheet;
import io.keikai.range.SImporters;
import org.zkoss.util.media.Media;
import org.zkoss.zk.ui.event.Events;
import org.zkoss.zul.Fileupload;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class Unreal2EntityResultWnd extends ResultWnd {
    @Override
    public void input() {
        Media media = Fileupload.get();
        if (null != media && media.getFormat().endsWith("xlsx")) {
            try {
                baseDao.execute("TRUNCATE table cisdi_unreal_entity");
                List<CisdiUnrealEntity> saveList = new ArrayList<>();
                SBook sbook = SImporters.getImporter().imports(media.getStreamData(), media.getName());
                for (SSheet sheet : sbook.getSheets()) {
                    Map<Integer, String> header = new HashMap<>();
                    for (int i = 0; i < sheet.getEndRowIndex(); i++) {
                        if (StrUtil.containsAny(sheet.getCell(i, 1).getStringValue(), "虚拟部件")) {
                            for (int j = 2; j <= sheet.getEndCellIndex(i); j++) {
                                if(StrUtil.isNotBlank(sheet.getCell(i,j).getStringValue())){
                                    header.put(j, sheet.getCell(i, j).getStringValue());
                                }
                            }
                        } else {
                            int finalI = i;
                            if (StrUtil.isNotBlank(sheet.getCell(finalI, 1).getStringValue())) {
                                header.forEach((k, v) -> {
                                    if (!StrUtil.equals(v, "备注")) {
                                        CisdiUnrealEntity entity = new CisdiUnrealEntity();
                                        entity.setUnrealPart(sheet.getCell(finalI, 1).getStringValue());
                                        entity.setPartType(v);
                                        entity.setPartValue(sheet.getCell(finalI, k).getStringValue());
                                        entity.setTblType(sheet.getSheetName());
                                        saveList.add(entity);
                                    }
                                });
                            }
                        }
                    }
                }
                baseDao.save(saveList);
                Events.postEvent("onSearch", this, true);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

}
