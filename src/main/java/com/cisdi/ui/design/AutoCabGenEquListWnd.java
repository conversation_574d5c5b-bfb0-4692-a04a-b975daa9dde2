package com.cisdi.ui.design;

import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.cisdi.entity.CisdiAutoCab;
import com.cisdi.entity.CisdiAutoCabGen;
import com.cisdi.entity.CisdiProject;
import com.cisdi.util.CisdiUtils;
import com.sys.common.CacheUtil;
import com.sys.common.Constants;
import com.sys.common.Utils;
import com.sys.core.zul.ListWnd;
import com.sys.core.zul.TopTree;
import com.sys.entity.Appconfig;
import com.sys.entity.TableInfo;

import java.io.File;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 0.0.1
 * @description AutoCabGenEquListWnd
 * @since 2024/8/8 14:06
 */
public class AutoCabGenEquListWnd extends ListWnd {

    @Override
    public void export() {
        CisdiProject project = Utils.getUserInfo().getProject();
//        List<Object> datas = this.getBaseDao().find(this.getDbClass(), -1, -1, super.b);
        String hql = "from CisdiAutoCabGen where proId="+project.getId();
        List<CisdiAutoCabGen> datas = baseDao.findHql(hql);

        try {


            ExcelWriter bigWriter = ExcelUtil.getBigWriter();
            List<TableInfo> infos = CacheUtil.getTableInfo("CISDI_AUTO_CAB_GEN");
            int i = 0;
            Iterator var5 = getCfgList().iterator();

            while(var5.hasNext()) {
                Appconfig cfg = (Appconfig)var5.next();
                Iterator var7 = infos.iterator();

                while(var7.hasNext()) {
                    TableInfo info = (TableInfo)var7.next();
                    if (StrUtil.equals(cfg.getDispName(), info.getDispname())) {
                        bigWriter.addHeaderAlias(cfg.getDispName(), cfg.getCfgDes());
                        bigWriter.setColumnWidth(i++, 20);
                    }
                }
            }

            File file = new File(Constants.resourceLocation + File.separator + "download" + File.separator + project.getId()+"_预规划箱柜信息确认表.xlsx");
            bigWriter.write(datas, true);
            bigWriter.flush(file);
            bigWriter.close();
            String webUrl = Constants.webUrl + "/download/" + file.getName();
            CisdiUtils.eplanDownd(webUrl);
        } catch (Exception var9) {
            var9.printStackTrace();
        }
    }

    @Override
    public void change(Object data, String field, Object orgVal, Object value) {
        if (StrUtil.equalsAny(field,"heightCode","seat")){
            CisdiAutoCabGen cabGen = (CisdiAutoCabGen) data;
            String oldCode = cabGen.getCabCode();
            cabGen.setCabCode(StrUtil.concat(true, cabGen.getHeightCode(), cabGen.getSeat()));
            baseDao.update(cabGen);
            String oldId = cabGen.getId();
            String newId = String.format("P%s>%s", cabGen.getProId(), cabGen.getCabCode());
//            cabGen.setId();
            //修改机架模块，回路
            Map param = new HashMap();
            param.put("oldId",oldId);
            param.put("proId",cabGen.getProId());
            param.put("cabId",newId);
            param.put("cabCode",cabGen.getCabCode());
            param.put("oldCode",oldCode);

            baseDao.execute("update cisdi_auto_rack_gen set cab_id=:cabId,cab_code=:cabCode where cab_id=:oldId", param);
            baseDao.execute("update cisdi_auto_module_gen set cab_id=:cabId,cab_code=:cabCode where cab_id=:oldId", param);
            baseDao.execute("update cisdi_template_equ_gen set cab_code=:cabCode where pro_id=:proId and cab_code=:oldCode", param);
            baseDao.execute("update cisdi_auto_cab_gen set id=:cabId where id=:oldId", param);
        }else  if (StrUtil.equalsAny(field,"heightCodeDes","seatDes")){
            //更新柜名
            CisdiAutoCabGen autoCab = (CisdiAutoCabGen) data;
            autoCab.setCabName(StrUtil.concat(true,autoCab.getHeightCodeDes(),autoCab.getSeatDes()));
            super.change(data, field, orgVal, value);
        } else {
            super.change(data, field, orgVal, value);
        }
        IOSetWnd wnd = (IOSetWnd) getParentWnd();
        wnd.initCabDesign();
        TopTree tree = (TopTree) wnd.getFellow("tree");
        tree.initData();
    }
}
