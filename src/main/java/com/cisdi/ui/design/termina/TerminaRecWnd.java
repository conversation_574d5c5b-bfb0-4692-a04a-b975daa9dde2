package com.cisdi.ui.design.termina;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.cisdi.entity.CisdiProject;
import com.cisdi.ui.DesignChildWnd;
import com.sys.common.Utils;
import com.sys.core.zul.RecWnd;
import org.zkoss.zk.ui.event.Events;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class TerminaRecWnd extends RecWnd {


    public void eprPlot() {
        CisdiProject cisdiProject  = getMainData();
        List<CisdiProject> lists = Utils.openList("termina_pro",cisdiProject, true, this);
//        if (ObjectUtil.isNotEmpty(lists)) {
//            CisdiProject project = lists.get(0);
//            CisdiProject main = getMainData();
//            JSONObject json = main.getJson();
//            JSONObject termina = new JSONObject();
//            termina.put("plotNum", project.getPlotNum());
//            termina.put("plotName", project.getPlotName());
//            json.put("termina", termina);
//            main.setTerminaPlotNum(project.getPlotNum());
//            baseDao.update(main);
//            getDataBinder().loadAll();
//        }

        if (ObjectUtil.isNotEmpty(lists)) {
            //置空
            Map param = new HashMap();
            param.put("proCode",cisdiProject.getProCode());
            String hql = "from CisdiProject where proCode=:proCode";
            List<CisdiProject> cisdiProjectList = baseDao.findHql(hql, param);
            cisdiProjectList.forEach(x ->{
                x.setTerminaPlotNum(null);
                JSONObject json = x.getJson();
                if (json.containsKey("plotNum")){
                    json.remove("plotNum");
                }
                if (json.containsKey("plotName")){
                    json.remove("plotName");
                }
            });
            if (CollectionUtil.isNotEmpty(cisdiProjectList)){
                baseDao.update(cisdiProjectList);
            }

            //判断是否填写套图顺序，如果有一个未填，则是为未填
            boolean needSort = false;
            if (lists.stream().filter(x->x.getPlotSort() == null).count() > 0){
                needSort = true;
            }
            lists = lists.stream().sorted(Comparator.comparing(CisdiProject::getPlotNum)).collect(Collectors.toList());

            for (int i = 0; i < lists.size(); i++) {
                CisdiProject main = getMainData();
                CisdiProject project = lists.get(i);
                JSONObject json = project.getJson();
                JSONObject termina = new JSONObject();
                termina.put("plotNum", main.getPlotNum());
                termina.put("plotName", main.getPlotName());
                json.put("termina", termina);
                project.setTerminaPlotNum(main.getPlotNum());
                if (needSort){
                    project.setPlotSort(i+1);
                }
            }
            baseDao.update(lists);
            getDataBinder().loadAll();
            Events.postEvent("onSearch", getListWnd("terminaBindProjectList"), true);
        }
    }

    @Override
    public void listClear(String listname) {
        if (StrUtil.equals(listname,"termina_pro")){
            CisdiProject project = getMainData();
            //清空关联信息
            Map param = new HashMap();
            param.put("proCode",project.getProCode());
            String hql = "from CisdiProject where proCode=:proCode";
            List<CisdiProject> cisdiProjectList = baseDao.findHql(hql, param);
            cisdiProjectList.forEach(x ->{
                x.setTerminaPlotNum(null);
                JSONObject json = x.getJson();
                if (json.containsKey("plotNum")){
                    json.remove("plotNum");
                }
                if (json.containsKey("plotName")){
                    json.remove("plotName");
                }
            });
            if (CollectionUtil.isNotEmpty(cisdiProjectList)){
                baseDao.update(cisdiProjectList);
            }
        }
        super.listClear(listname);
        Events.postEvent("onSearch", getListWnd("terminaBindProjectList"), true);
    }

    public void submit(){
        DesignChildWnd design = (DesignChildWnd) parentWnd;
        design.updateUi(2);
    }
}
