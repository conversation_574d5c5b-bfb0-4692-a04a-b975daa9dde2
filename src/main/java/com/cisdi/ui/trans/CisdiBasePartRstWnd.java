package com.cisdi.ui.trans;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.cisdi.entity.trans.CisdiTransBasePart;
import com.sys.core.zul.ResultWnd;
import org.zkoss.util.media.Media;
import org.zkoss.zk.ui.Executions;
import org.zkoss.zul.Fileupload;
import org.zkoss.zul.Window;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

public class CisdiBasePartRstWnd extends ResultWnd {
    @Override
    public void input() {
        Media media = Fileupload.get();
        if (null != media && StrUtil.startWith(media.getFormat(), "xls")) {
            ExcelReader reader = ExcelUtil.getReader(media.getStreamData());
            List<CisdiTransBasePart> saveList = new ArrayList<>();
            Map param = new HashMap();
            param.put("cab_type","虚拟部件");
            param.containsKey("cab_type");

            Window window = (Window) Executions.createComponents("~./page/cisdi/trans/up_scheme_meter.zul", null, param);
            window.doModal();
            Map<String, String> result = (Map) window.getAttribute("data");
            for (String name : reader.getSheetNames()) {
                if (!StrUtil.equals(reader.getSheet().getSheetName(), name)) {
                    reader = ExcelUtil.getReader(media.getStreamData(), name);
                }
                List<List<Object>> datas = reader.read();
                Map<Integer, String> header = new HashMap<>();
                Map<Integer, String> attribTypes = new HashMap();
                boolean signMachine = false;
                for (List<Object> data : datas) {
                    if (data.stream().filter(e -> ObjectUtil.equal(e, "单线宏")).count() > 0) {//表头
                        header.clear();
                        attribTypes.clear();
                        for (int i = 0; i < data.size(); i++) {
                            if (ObjectUtil.isNotEmpty(data.get(i))) {
                                if (ObjectUtil.contains(data.get(i), "单线宏")) {
                                    header.put(i, "macroName");
                                } else if (ObjectUtil.contains(data.get(i), "电流")) {
                                    header.put(i, "electricity");
                                } else if (ObjectUtil.contains(data.get(i), "功率")) {
                                    header.put(i, "power");
                                    if (String.valueOf(data.get(i)).contains("单个电机")) {
                                        signMachine = true;
                                    }
                                } else if (((String) data.get(i)).length() > 1) {
                                    attribTypes.put(i, (String) data.get(i));
                                }

                            }
                        }
                    } else {//记录
                        boolean finalSignMachine = signMachine;
                        attribTypes.forEach((k, v) -> {
                            if (data.size() > k) {
                                try {
                                    CisdiTransBasePart basePart = new CisdiTransBasePart();
                                    header.forEach((m, n) -> {
                                        BeanUtil.setFieldValue(basePart, n, data.get(m));
                                    });
                                    basePart.setPartType(v);
                                    basePart.setPath(name.replaceFirst("-", "\\\\")+"\\");
                                    basePart.setPartType(basePart.getPartType().replace("-", "_"));
                                    basePart.setPartValue(String.valueOf(data.get(k)));
                                    basePart.setSignMachine(finalSignMachine);
                                    basePart.setProcessType(result.get("process_type"));
                                    if (basePart.getMacroName().contains("\n")) {
                                        String macros[] = basePart.getMacroName().split("\n");
                                        for (String macro : macros) {
                                            CisdiTransBasePart add = new CisdiTransBasePart();
                                            BeanUtil.copyProperties(basePart, add);
                                            add.setMacroName(basePart.getPath() + macro);
                                            add.setProcessType(result.get("process_type"));
                                            saveList.add(add);
                                        }
                                    } else {
                                        basePart.setMacroName(basePart.getPath() + basePart.getMacroName());
                                        saveList.add(basePart);
                                    }
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            }
                        });
                    }
                }
            }
            if (!saveList.isEmpty()) {
//                baseDao.execute("TRUNCATE table cisdi_trans_base_part");
                baseDao.execute("delete from cisdi_trans_base_part where process_type='"+result.get("process_type")+"'");
                List<String> macroList = saveList.stream().filter(e -> e.getSignMachine()).map(e -> e.getMacroName()).collect(Collectors.toList());
                Map<String, Object> map = new HashMap<>();
                map.put("macros", macroList);
                String sql = "update cisdi_trans_loop_scheme set sign_machine=1 where single_macro in (:macros)";
                baseDao.execute(sql, map);
                baseDao.save(saveList);
            }
        }
    }
}
