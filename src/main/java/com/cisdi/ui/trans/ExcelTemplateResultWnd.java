package com.cisdi.ui.trans;

import cn.hutool.core.util.StrUtil;
import com.cisdi.entity.trans.CisdiTransConfigList;
import com.cisdi.entity.trans.CisdiTransExcelTemplate;
import com.cisdi.util.CisdiUtils;
import com.sys.common.Constants;
import com.sys.common.Message;
import com.sys.common.Utils;
import com.sys.core.zul.ResultWnd;
import com.sys.core.zul.api.EnumType;
import com.sys.entity.ButtonCfg;
import org.zkoss.util.media.Media;
import org.zkoss.zhtml.Messagebox;
import org.zkoss.zk.ui.event.Event;
import org.zkoss.zul.Filedownload;
import org.zkoss.zul.Fileupload;
import org.zkoss.zul.Listitem;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 * @version 0.0.1
 * @description AddSchemeRecWnd
 * @since 2024/1/17 15:14
 */
public class ExcelTemplateResultWnd extends ResultWnd {

    @Override
    public boolean checkBtncfg(Object data, ButtonCfg cfg) {
        if (getParam("from") != null && StrUtil.equals(cfg.getMethodName(),"uploadTemplate")){
            return false;
        }else {
            return true;
        }
    }

    public void uploadTemplate(Event event) throws IOException {
        Map<String, Object> data = (Map) event.getData();
        Listitem item = (Listitem) data.get("target");
        CisdiTransExcelTemplate template = item.getValue();

        Media media = Fileupload.get();
        if (media == null || !media.getFormat().startsWith("xls")) {
            Messagebox.show("文件选择有误，请选择xls文件", "错误", Messagebox.OK, Messagebox.ERROR);
            return;
        }
        String valueType = template.getTmpPath().substring(template.getTmpPath().lastIndexOf("\\")+1,template.getTmpPath().length());
        String cabType = template.getTmpCabType().contains("trans_fixed") ? "固定柜" : "抽屉柜";

        Map param = new HashMap();
        param.put("value_type",valueType);
        param.put("cab_type",cabType);
        param.put("circuit_num",template.getCircuitNum());
        //更新值列表
        baseDao.execute("delete from cisdi_trans_config_list where (list_name = 'loop_type' or list_name = 'home_system' or list_name = 'install_seat') and value_type =:value_type and cab_type=:cab_type and circuit_num=:circuit_num",param);
        List<Map<String, Object>> list = Utils.readMap(media, "回路类型标签");




        List<CisdiTransConfigList> listvalues = new ArrayList<>();
        Integer homeSortNo = 1;
        Integer loopSortNo = 1;
        Integer installSortNo = 1;
        for (Map<String, Object> map : list) {
            Object homeSystem = map.get("归属系统");
            if (homeSystem != null){
                CisdiTransConfigList configList = new CisdiTransConfigList();
                configList.setListName("home_system");
                configList.setListValue(String.valueOf(homeSystem));
                configList.setValueType(valueType);
                configList.setCabType(cabType);
                configList.setSortNo(homeSortNo);
                configList.setCircuitNum(template.getCircuitNum());
                homeSortNo ++ ;
                listvalues.add(configList);
            }

            Object installListValue = map.get("安装位置");
            if (installListValue != null){
                CisdiTransConfigList installList = new CisdiTransConfigList();
                installList.setListName("install_seat");
                installList.setListValue(String.valueOf(installListValue));
                installList.setValueType(valueType);
                installList.setCabType(cabType);
                installList.setSortNo(installSortNo);
                installList.setCircuitNum(template.getCircuitNum());
                installSortNo ++ ;
                listvalues.add(installList);
            }

            Object loopListValue = map.get("回路类型");
            if (loopListValue != null){
                CisdiTransConfigList loopList = new CisdiTransConfigList();
                loopList.setListName("loop_type");
                loopList.setListValue(String.valueOf(loopListValue));
                loopList.setValueType(valueType);
                loopList.setCabType(cabType);
                loopList.setSortNo(loopSortNo);
                loopList.setCircuitNum(template.getCircuitNum());
                loopSortNo ++ ;
                listvalues.add(loopList);
            }
        }
        baseDao.save(listvalues);
//        SBook sbook = SImporters.getImporter().imports(media.getStreamData(), media.getName());
//        SSheet sheet = sbook.getSheetByName("回路类型标签");
//        sheet.setColMap(Class<Object> Object,1);
//        List<Object> sheetData = sheet.getSheetData();
        String path = template.getTmpPath()+File.separator+template.getCircuitNum()+"_"+template.getTmpCabType();

        File file = new File(path);
        file.getParentFile().mkdirs();
        CisdiUtils.booksList.clear();
        try {
            FileOutputStream fos = new FileOutputStream(file);
            fos.write(media.getByteData());
            fos.close();
        } catch (Exception e) {
            e.printStackTrace();
            Message.showError("导出失败");
        }
    }

    public void downLoadTemplate(Event event) {
        Map<String, Object> data = (Map) event.getData();
        Listitem item = (Listitem) data.get("target");
        CisdiTransExcelTemplate template = item.getValue();
        String path = template.getTmpPath()+File.separator+template.getCircuitNum()+"_"+template.getTmpCabType();
        try {
            if (getParam("from") == null){
                Filedownload.save(new File(path), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset:utf-8");
            }else {
                String filePath = Constants.webUrl + "/files/" + path.replace("\\","/").replace(Constants.getProperty("resourceLocation"),"");
                CisdiUtils.eplanDownd(filePath);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void save() {
        String hql ="from CisdiTransExcelTemplate";
        List<CisdiTransExcelTemplate> excelTemplate = baseDao.findHql(hql);
        AtomicBoolean flag = new AtomicBoolean(false);
        this.update.get(EnumType.UPDATE_TYPE.ADD).stream().map(x ->(CisdiTransExcelTemplate)x).forEach(t ->{
            long count = excelTemplate.stream().filter(x -> StrUtil.equals(t.getTmpPath(), x.getTmpPath()) && StrUtil.equals(t.getTmpCabType(), x.getTmpCabType()) && StrUtil.equals(t.getCircuitNum(),x.getCircuitNum())).count();
            if (count > 0){
                Message.showWarning("模板："+t.getTmpName()+",在系统中已存在，无需添加");
                flag.set(true);
            }
        });
        if (flag.get()){
            return;
        }

        super.save();
    }
}
