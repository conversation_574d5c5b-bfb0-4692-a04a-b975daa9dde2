package com.fund.domain;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

@Entity
@Data
@Table(name = "STOCK_LIST", schema = "FUND")
public class StockList implements java.io.Serializable {
    @Id
    @GenericGenerator(name = "STOCK_LIST", strategy = "assigned")
    private String f12;
    private Integer f13;
    private String f14;

    public boolean equals(Object obj) {
        if (obj instanceof StockList) {
            StockList t = (StockList) obj;
            return f12 != null ? f12.equals(t.f12) : super.equals(obj);
        } else {
            return super.equals(obj);
        }
    }

    public int hashCode() {
        return f12 != null ? f12.hashCode() : super.hashCode();
    }
}
