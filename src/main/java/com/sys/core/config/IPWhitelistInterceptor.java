package com.sys.core.config;


import cn.hutool.core.util.StrUtil;
import com.sys.common.Utils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.servlet.HandlerInterceptor;

import java.util.Arrays;
import java.util.List;

public class IPWhitelistInterceptor implements HandlerInterceptor {

    private List<String> whitelist;

    public IPWhitelistInterceptor(String... whitelist) {
        this.whitelist = Arrays.asList(whitelist);
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        String ip = Utils.getIpAddress(request);
        if (whitelist.stream().anyMatch(e -> StrUtil.startWith(ip, e))) {
            return true;
        } else {
            response.setStatus(HttpServletResponse.SC_FORBIDDEN); // 不在白名单内，返回403禁止访问
            return false;
        }
    }
}
