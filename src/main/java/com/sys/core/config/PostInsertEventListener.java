package com.sys.core.config;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ReflectUtil;
import com.sys.core.api.BaseEntity;
import org.hibernate.event.spi.PreInsertEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.Calendar;

@Component
public class PostInsertEventListener implements org.hibernate.event.spi.PreInsertEventListener {
    @Override
    public boolean onPreInsert(PreInsertEvent event) {
        Object entity = event.getEntity();
        if (entity instanceof BaseEntity base && base.getCreateTime() == null) {
            base.setCreateTime(Calendar.getInstance().getTime());
            base.setUpdateTime(Calendar.getInstance().getTime());
        }
        return false;
    }
}
