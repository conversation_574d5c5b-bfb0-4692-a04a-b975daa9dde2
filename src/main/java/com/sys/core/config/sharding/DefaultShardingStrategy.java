package com.sys.core.config.sharding;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.sys.core.cache.CacheServer;
import com.sys.entity.MainTable;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.Map;

/**
 * 默认分片策略实现
 * 基于CacheServer中的getTableShard方法获取分片配置
 */
@Component
public class DefaultShardingStrategy implements ShardingStrategy {

    private static final Logger log = LoggerFactory.getLogger(DefaultShardingStrategy.class);

    @Autowired
    private CacheServer cacheServer;

    @Value("${app.sharding.default-key-field:id}")
    private String defaultKeyField;

    @PostConstruct
    public void init() {
        log.info("分片策略初始化完成，使用CacheServer获取分片配置");
    }

    @Override
    public String calculateShardSuffix(Object entity) {
        if (entity == null) {
            return null;
        }

        String tableName = getTableName(entity.getClass());
        if (!needSharding(tableName)) {
            return null;
        }

        // 获取分片配置
        Map<String, Map<String, String>> shardConfig = cacheServer.getTableShard(tableName);
        if (shardConfig.isEmpty()) {
            return null;
        }

        try {
            // 遍历分片字段配置
            for (Map.Entry<String, Map<String, String>> entry : shardConfig.entrySet()) {
                String fieldName = entry.getKey();
                Map<String, String> valueShardMap = entry.getValue();

                // 获取实体字段值
                Field field = ReflectUtil.getField(entity.getClass(), fieldName);
                if (field != null) {
                    field.setAccessible(true);
                    Object fieldValue = field.get(entity);
                    if (fieldValue != null) {
                        String fieldValueStr = String.valueOf(fieldValue);
                        // 查找匹配的分片
                        String shard = valueShardMap.get(fieldValueStr);
                        if (StrUtil.isNotBlank(shard)) {
                            log.info("实体 {} 字段 {} 值 {} 匹配到分片: {}",
                                entity.getClass().getSimpleName(), fieldName, fieldValueStr, shard);
                            return shard;
                        } else {
                            log.warn("字段值 {} 没有匹配的分片配置", fieldValueStr);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取分片键值失败: {}", e.getMessage());
        }

        return null;
    }



    @Override
    public String getShardingKeyField(String tableName) {
        // 获取分片配置
        Map<String, Map<String, String>> shardConfig = cacheServer.getTableShard(tableName);
        if (!shardConfig.isEmpty()) {
            // 返回第一个分片字段名
            return shardConfig.keySet().iterator().next();
        }
        return defaultKeyField;
    }

    @Override
    public boolean needSharding(String tableName) {
        // 检查是否有分片配置
        Map<String, Map<String, String>> shardConfig = cacheServer.getTableShard(tableName);
        return !shardConfig.isEmpty();
    }

    /**
     * 获取实体类对应的表名
     */
    private String getTableName(Class<?> entityClass) {
        // 通过CacheServer获取MainTable信息
        MainTable mainTable = cacheServer.getMainTable(entityClass.getName());
        if (mainTable != null && StrUtil.isNotBlank(mainTable.getTabName())) {
            return mainTable.getTabName();
        }
        // 如果没有找到配置，使用类名转下划线作为表名
        return StrUtil.toUnderlineCase(entityClass.getSimpleName());
    }
}
