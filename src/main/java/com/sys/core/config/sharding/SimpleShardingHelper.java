package com.sys.core.config.sharding;

import cn.hutool.core.util.StrUtil;
import com.sys.core.cache.CacheServer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 简单分片助手
 * 用于处理单个事务中的多分片操作
 */
@Component
public class SimpleShardingHelper {

    private static final Logger log = LoggerFactory.getLogger(SimpleShardingHelper.class);

    @Autowired
    private CacheServer cacheServer;

    @Autowired
    private DefaultShardingStrategy shardingStrategy;

    // 用于跟踪是否已经注册了事务同步器
    private static final ThreadLocal<Boolean> syncRegistered = new ThreadLocal<Boolean>() {
        @Override
        protected Boolean initialValue() {
            return false;
        }
    };

    /**
     * 执行分片操作
     * 在同一个事务中按分片顺序执行操作
     *
     * @param entities 实体列表
     * @param operation 操作
     * @param <T> 实体类型
     */
    public <T> void executeWithSharding(List<T> entities, ShardOperation<T> operation) {
        if (entities == null || entities.isEmpty()) {
            return;
        }

        // 注册事务同步器，在事务完成后清除分片上下文
        registerTransactionSync();

        // 按分片分组
        Map<String, List<T>> shardGroups = groupEntitiesByShard(entities);

        // 按分片顺序执行操作
        for (Map.Entry<String, List<T>> entry : shardGroups.entrySet()) {
            String shardSuffix = entry.getKey();
            List<T> shardEntities = entry.getValue();

            if (shardEntities.isEmpty()) {
                continue;
            }

            try {
                // 清除之前的分片上下文
                TableNameContext.clear();

                // 设置当前分片上下文
                String tableName = getTableName(shardEntities.get(0).getClass()).toLowerCase();
                setShardContext(tableName, shardSuffix);

                log.info("执行分片操作: 表={}, 分片={}, 实体数量={}", tableName, shardSuffix, shardEntities.size());

                // 逐个处理实体，确保每个实体操作时分片上下文都正确
                for (T entity : shardEntities) {
                    // 重新设置分片上下文，确保每个操作都有正确的上下文
                    TableNameContext.setSuffix(tableName, shardSuffix);

                    // 验证分片上下文
                    String currentSuffix = TableNameContext.getSuffix(tableName);
                    log.info("处理实体前验证分片上下文: 表={}, 期望后缀={}, 实际后缀={}",
                        tableName, shardSuffix, currentSuffix);

                    // 执行单个实体操作
                    operation.execute(java.util.Arrays.asList(entity));

                    log.info("实体操作完成: 表={}, 分片={}", tableName, shardSuffix);
                }

                log.info("分片 {} 操作完成，实体数量: {}", shardSuffix, shardEntities.size());

            } catch (Exception e) {
                log.error("分片 {} 操作失败: {}", shardSuffix, e.getMessage(), e);
                throw new RuntimeException("分片操作失败: " + shardSuffix, e);
            }
        }
    }

    /**
     * 注册事务同步器
     */
    private void registerTransactionSync() {
        if (TransactionSynchronizationManager.isSynchronizationActive() && !syncRegistered.get()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCompletion(int status) {
                    TableNameContext.clear();
                    syncRegistered.set(false);
                    log.debug("事务完成后清除分片上下文");
                }
            });
            syncRegistered.set(true);
            log.debug("注册事务同步器，将在事务完成后清除分片上下文");
        }
    }

    /**
     * 根据分片策略对实体列表进行分组
     *
     * @param entities 实体列表
     * @return 分片后缀 -> 实体列表的映射
     */
    public <T> Map<String, List<T>> groupEntitiesByShard(List<T> entities) {
        if (entities == null || entities.isEmpty()) {
            return Collections.emptyMap();
        }

        Map<String, List<T>> shardGroups = new LinkedHashMap<>();

        for (T entity : entities) {
            String shardSuffix = shardingStrategy.calculateShardSuffix(entity);
            log.info("实体分片计算结果: 实体={}, 分片={}", entity.getClass().getSimpleName(), shardSuffix);

            if (StrUtil.isBlank(shardSuffix)) {
                // 不需要分片的实体放在默认组
                log.info("实体 {} 不需要分片，放入default组", entity.getClass().getSimpleName());
                shardGroups.computeIfAbsent("default", k -> new ArrayList<>()).add(entity);
            } else {
                log.info("实体 {} 分配到分片: {}", entity.getClass().getSimpleName(), shardSuffix);
                shardGroups.computeIfAbsent(shardSuffix, k -> new ArrayList<>()).add(entity);
            }
        }

        log.info("实体分片分组结果: {}",
            shardGroups.entrySet().stream()
                .collect(Collectors.toMap(
                    Map.Entry::getKey,
                    e -> e.getValue().size()
                ))
        );

        return shardGroups;
    }

    /**
     * 设置分片上下文
     *
     * @param tableName 表名
     * @param shardSuffix 分片后缀
     */
    private void setShardContext(String tableName, String shardSuffix) {
        if (StrUtil.isNotBlank(shardSuffix) && !"default".equals(shardSuffix)) {
            TableNameContext.setSuffix(tableName, shardSuffix);
            log.info("设置分片上下文: {} -> {}", tableName, shardSuffix);

            // 验证分片上下文是否设置成功
            String actualSuffix = TableNameContext.getSuffix(tableName);
            log.info("验证分片上下文: {} 实际后缀: {}", tableName, actualSuffix);
        } else {
            log.info("不需要分片或使用默认分片: 表={}, 后缀={}", tableName, shardSuffix);
        }
    }

    /**
     * 获取实体对应的表名
     */
    private String getTableName(Class<?> entityClass) {
        // 通过CacheServer获取MainTable信息
        try {
            var mainTable = cacheServer.getMainTable(entityClass.getName());
            if (mainTable != null && StrUtil.isNotBlank(mainTable.getTabName())) {
                return mainTable.getTabName();
            }
        } catch (Exception e) {
            log.warn("获取表名失败: {}", e.getMessage());
        }
        // 如果没有找到配置，使用类名转下划线作为表名
        return StrUtil.toUnderlineCase(entityClass.getSimpleName());
    }

    /**
     * 分片操作接口
     */
    @FunctionalInterface
    public interface ShardOperation<T> {
        void execute(List<T> entities) throws Exception;
    }
}
