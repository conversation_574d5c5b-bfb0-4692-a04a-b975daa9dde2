package com.sys.core.config.sharding;

import cn.hutool.core.util.StrUtil;
import com.sys.core.cache.CacheServer;
import com.sys.core.server.SpringContextHolder;
import org.hibernate.resource.jdbc.spi.StatementInspector;

import java.util.regex.Matcher;
import java.util.regex.Pattern;


public class TableNameStatementInspector implements StatementInspector {

    CacheServer cacheServer;

    private CacheServer getCacheServer() {
        if (cacheServer == null) {
            try {
                cacheServer = SpringContextHolder.getBean(CacheServer.class);
            } catch (Exception e) {
                return null;
            }
        }
        return cacheServer;
    }

    @Override
    public String inspect(String sql) {
        if (getCacheServer() != null) {
            String logicTable = extractTableName(sql);
            String suffix = TableNameContext.getSuffix(logicTable);

            // 添加详细日志
            System.out.println("=== SQL拦截 ===");
            System.out.println("原始SQL: " + sql);
            System.out.println("提取表名: " + logicTable);
            System.out.println("分片后缀: " + suffix);

            if (!StrUtil.equals(suffix, "default") && sql.contains(logicTable)) {
                String newSql = sql.replaceAll("(?i)\\b" + logicTable + "\\b", logicTable + "_" + suffix);
                System.out.println("修改后SQL: " + newSql);
                System.out.println("===============");
                return newSql;
            } else {
                System.out.println("SQL未修改");
                System.out.println("===============");
            }
        }
        return sql;
    }

    public String extractTableName(String sql) {
        if (sql == null) return null;
        sql = sql.trim().toLowerCase();
        Pattern select = Pattern.compile("from\\s+([`\\w]+)", Pattern.CASE_INSENSITIVE);
        Pattern insert = Pattern.compile("insert\\s+into\\s+([`\\w]+)", Pattern.CASE_INSENSITIVE);
        Pattern update = Pattern.compile("update\\s+([`\\w]+)", Pattern.CASE_INSENSITIVE);
        Pattern delete = Pattern.compile("delete\\s+from\\s+([`\\w]+)", Pattern.CASE_INSENSITIVE);
        Matcher m;
        if ((m = select.matcher(sql)).find()) return m.group(1).replace("`", "");
        if ((m = insert.matcher(sql)).find()) return m.group(1).replace("`", "");
        if ((m = update.matcher(sql)).find()) return m.group(1).replace("`", "");
        if ((m = delete.matcher(sql)).find()) return m.group(1).replace("`", "");
        return null;
    }
}
