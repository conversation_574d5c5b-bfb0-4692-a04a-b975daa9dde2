package com.sys.core.server;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.sys.common.Utils;
import com.sys.core.api.AsyncService;
import com.sys.core.api.BaseDao;
import com.sys.core.cache.CacheEventPublisher;
import com.sys.core.cache.CacheServer;
import com.sys.core.config.sharding.SimpleShardingHelper;
import com.sys.core.config.sharding.TableNameContext;
import com.sys.core.zul.api.EnumType.UPDATE_TYPE;
import com.sys.entity.MainTable;
import com.sys.entity.Refresh;
import com.sys.jpa.*;
import jakarta.persistence.*;
import jakarta.persistence.criteria.*;
import jakarta.persistence.metamodel.Metamodel;
import org.hibernate.Session;
import org.hibernate.query.sql.internal.NativeQueryImpl;
import org.hibernate.transform.Transformers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Persistable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.support.JpaEntityInformation;
import org.springframework.data.jpa.repository.support.JpaMetamodelEntityInformation;
import org.springframework.data.jpa.repository.support.JpaPersistableEntityInformation;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.Assert;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;
import java.util.function.Consumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static org.springframework.data.jpa.repository.query.QueryUtils.toOrders;

@Service("BaseDao")
@SuppressWarnings("all")
public class BaseImpl implements BaseDao {
    private static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(BaseImpl.class);
    @PersistenceContext
    public EntityManager em;
    LogServer logServer;

    @Value("${app.cache.mode:local}")
    private String cacheMode;
    @Autowired
    private CacheServer cacheServer;
    @Autowired
    private AsyncService asyncService;
    @Autowired
    private CacheEventPublisher cacheEventPublisher;
    @Autowired
    private SimpleShardingHelper shardingHelper;

    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    public boolean execute(String... sql) {
        try {
            Arrays.stream(sql).toList().forEach(t -> getQuery(t, "sql", null).executeUpdate());
        } catch (Exception e) {
            log.error("Sql提交错误:{}", sql, e);
            e.printStackTrace();
            return false;
        }
        return true;
    }

    @Transactional(isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    public boolean execute(String sql, Map<String, Object> param) {
        try {
            getQuery(sql, "sql", param).executeUpdate();
        } catch (Exception e) {
            log.error("execute:{}", e);
            return false;
        }
        return true;
    }

    @Transactional(isolation = Isolation.READ_COMMITTED)
    public boolean save(Object data) {
        List<Object> list = Utils.toList(data);
        try {
            shardingHelper.executeWithSharding(list, entities -> {
                entities.forEach(e -> em.persist(e));
            });
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                public void afterCommit() {
                    cacheServer.evictAllEntityCaches(list);
                }
            });
        } catch (Exception e) {
            log.warn("实体已存在: {}", e.getMessage());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }
        return true;
    }

    public void cachePublisher(List list) {
        asyncService.runAsync(() -> {
            for (Object entity : list) {
                Object id = getEntityId(entity);
                if (id != null) {
                    cacheEventPublisher.publishEntityCacheEvictEvent(entity.getClass(), id);
                }
            }
        });
    }

    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public boolean delete(Object data) {
        List<Object> list = Utils.toList(data);
        try {
            shardingHelper.executeWithSharding(list, entities -> {
                entities.forEach(e -> em.remove(em.contains(e) ? e : em.merge(e)));
            });

            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    cacheServer.evictAllEntityCaches(list);
                    cachePublisher(list);
                    log.debug("清除缓存: 删除数据 {}", list.stream().map(e -> e.getClass().getSimpleName()).distinct().toList());
                }
            });
        } catch (Exception e) {
            log.error("删除数据错误:{}", e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }
        return true;
    }

    @Transactional(isolation = Isolation.READ_COMMITTED)
    public boolean update(Object data) {
        List<Object> list = Utils.toList(data);
        try {
            shardingHelper.executeWithSharding(list, entities -> {
                try (Session session = em.unwrap(Session.class)) {
                    entities.forEach(session::update);
                }
            });
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                public void afterCommit() {
                    cacheServer.evictAllEntityCaches(list);
                    cachePublisher(list);
                    log.debug("清除缓存: 更新数据 {}", list.stream().map(e -> e.getClass().getSimpleName()).distinct().toList());
                }
            });
        } catch (Exception e) {
            log.error("修改数据错误:{}", e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }
        return true;
    }

//    @Transactional(isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class, timeout = 30)

    @Transactional(isolation = Isolation.READ_COMMITTED)
    public <T> boolean saveData(Map<UPDATE_TYPE, List<T>> datas) {
        List allChanged = Utils.mergeList(datas.values());
        try {
            datas.forEach((k, v) -> {
                if (v != null && !v.isEmpty()) {
                    // 使用分片事务管理器执行分片操作
                    try {
                        shardingHelper.executeWithSharding(v, entities -> {
                            if (k == UPDATE_TYPE.ADD) {
                                entities.forEach(e -> em.persist(e));
                            } else if (k == UPDATE_TYPE.UPDATE) {
                                update(v);
                            } else if (k == UPDATE_TYPE.DEL) {
                                entities.forEach(e -> em.remove(em.contains(e) ? e : em.merge(e)));
                            }
                        });
                    } catch (Exception e) {
                        throw new RuntimeException("分片操作失败", e);
                    }
                }
            });
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                public void afterCommit() {
                    cacheServer.evictAllEntityCaches(allChanged);
                    cachePublisher(allChanged);
                }
            });
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }
        return true;
    }

    @Transactional(readOnly = true)
    public <T> List<T> findHql(String hql) {
        log.debug("执行 HQL 查询: {}", hql);
        List<T> result = dbQuery(hql, "hql", null);
        return result;
    }

    @Transactional(readOnly = true)
    @Override
    public <T> List<T> findHql(String hql, Map<String, Object> param) {
        log.debug("执行 HQL 查询: {}, 参数: {}", hql, param);
        return dbQuery(hql, "hql", param);
    }

    @Cacheable(value = "entityCache", key = "T(com.sys.core.server.CacheKeyBuilder).build('entity', #clas.getSimpleName(), T(String).valueOf(#pk))")
    @Transactional(readOnly = true)
    public <T> T find(Class<T> clas, Object pk) {
        try {
            String cacheKey = CacheKeyBuilder.build("entity", clas.getSimpleName(), String.valueOf(pk));
            return cacheServer.getOrLoad("entityCache", cacheKey, () -> em.find(clas, pk));
        } catch (Exception e) {
            log.error("数据查询错误:实体{},{}", clas, pk, e);
            String cacheKey = CacheKeyBuilder.build("entity", clas.getSimpleName(), String.valueOf(pk));
            return null;
        }
    }

    @Transactional(readOnly = true)
    public <T> List<T> findSql(String sql) {
        log.debug("执行 SQL 查询: {}", sql);
        return findSql(sql, null);
    }

    @Transactional(readOnly = true)
    public <T> List<T> findSql(String sql, Map<String, Object> param) {
        log.debug("执行 SQL 查询: {}, 参数: {}", sql, param);
        return dbQuery(sql, "sql", param);
    }

    @Override
    @Transactional(readOnly = true)
    public <T> List<T> findSql(Class<T> clas, String sql) {
        return findSql(clas, sql, null);
    }

    @Override
    @Transactional(readOnly = true)
    public <T> List<T> findMapWithSql(String sql) {
        return dbQuery(sql, "sql", null, "map");
    }

    @Override
    @Transactional(readOnly = true)
    public <T> List<T> findMapWithHql(String hql) {
        return dbQuery(hql, "hql", null, "map");
    }

    @Override
    @Transactional(readOnly = true)
    public <T> List<T> findSql(Class<T> clas, String sql, Map<String, Object> param) {
        return BeanUtil.copyToList(findMapWithSql(sql, param), clas);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Map<String, Object>> findMapWithSql(String sql, Map<String, Object> param) {
        return dbQuery(sql, "sql", param, "map");
    }

    @Override
    @Transactional(readOnly = true)
    public List<Map<String, Object>> findMapWithHql(String hql, Map<String, Object> param) {
        return dbQuery(hql, "hql", param, "map");
    }

    @Transactional(readOnly = true)
    public <T> List<T> findRecursion(Class<T> clas, List<Object> roots) {
        log.debug("递归查询实体: {}, 根节点: {}", clas.getSimpleName(), roots);
        MainTable maintable = cacheServer.getMainTable(clas.getName());
        if (maintable != null) {
            Map<String, Object> param = new HashMap<>();
            param.put("roots", roots);
            String tabName = maintable.getTabName();
            String sql = "WITH RECURSIVE cte AS ( SELECT * FROM " + tabName + " WHERE parent_id in(:roots)   UNION ALL  SELECT t.* FROM " + tabName + " t INNER JOIN cte ON t.parent_id = cte.id ) SELECT * FROM cte";
            List<T> result = findSql(clas, sql, param);
            return result;
        }
        return null;
    }

    /**
     * 分页查询实体列表
     *
     * @param clas  实体类
     * @param start 起始位置
     * @param end   结束位置
     * @param conds 查询条件
     * @return 实体列表
     */
    @Transactional(readOnly = true)
    @Cacheable(value = "pageCache", key = "T(com.sys.core.server.CacheKeyBuilder).build('page', #clas.getSimpleName(), (#start + '-' + #end), T(String).valueOf(#conds != null ? #conds.hashCode() : 0))", unless = "#result == null || #result.isEmpty()")
    public <T> List<T> find(Class<T> clas, int start, int end, Map<String, Object> conds) {
        // 如果缓存模式为none，直接查询数据库
        if ("none".equalsIgnoreCase(cacheMode)) {
            return findNoCache(clas, start, end, conds);
        }
        try {
            String condsHash = String.valueOf(conds != null ? conds.hashCode() : 0);
            String cacheKey = CacheKeyBuilder.build("page", clas.getSimpleName(), start + "-" + end, condsHash);
            List<T> result = cacheServer.getOrLoad("pageCache", cacheKey, () -> {
                long queryStartTime = System.currentTimeMillis();
                List<T> data = findNoCache(clas, start, end, conds);
                return data;
            });
            return result;
        } catch (Exception e) {
            log.error("分页查询失败 - 实体: {}, 起始: {}, 结束: {}, 条件: {}", clas.getSimpleName(), start, end, conds, e);
            return new ArrayList<>();
        }
    }

    // 新增：无缓存的分页查询实现
    public <T> List<T> findNoCache(Class<T> clas, int start, int end, Map<String, Object> conds) {
        Query query = getQuery(clas, conds);
        if (start >= 0) {
            query.setFirstResult(start);
            query.setMaxResults(end);
        }
        MainTable mainTable = cacheServer.getMainTable(clas.getName());
        List<T> result = query.getResultList();
        if (null != mainTable) {
            List<String> shards = cacheServer.getTableShards(mainTable.getTabName());
            if (!shards.isEmpty()) {
                for (String shard : shards) {
                    try {
                        TableNameContext.setSuffix(mainTable.getTabName().toLowerCase(), shard);
                        result.addAll(query.getResultList());
                    } finally {
                        TableNameContext.clear();
                    }
                }
                Sort sort = getSort(conds);
                if (sort != null) {
                    sortListBySort(result, sort);
                    if (end < result.size()) {
                        result = result.subList(0, end);
                    }
                } else {
                    result = sortAndLimit(result, mainTable.getGenCol(), true, end);
                }
            }
        }
        return result;
    }

    public static <T> void sortListBySort(List<T> list, Sort sort) {
        if (sort == null || list == null || list.isEmpty()) return;
        Comparator<T> comparator = null;
        for (Sort.Order order : sort) {
            Comparator<T> fieldComparator = (a, b) -> {
                try {
                    String field = order.getProperty();
                    Method m = a.getClass().getMethod("get" + capitalize(field));
                    Comparable v1 = (Comparable) m.invoke(a);
                    Comparable v2 = (Comparable) m.invoke(b);
                    int cmp = v1 == null ? (v2 == null ? 0 : -1) : (v2 == null ? 1 : v1.compareTo(v2));
                    return order.isAscending() ? cmp : -cmp;
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            };
            comparator = comparator == null ? fieldComparator : comparator.thenComparing(fieldComparator);
        }
        if (comparator != null) {
            list.sort(comparator);
        }
    }

    public <T> List<T> sortAndLimit(List<T> result, String sortField, boolean desc, int topN) {
        return result.stream()
                .sorted((a, b) -> {
                    try {
                        Method m = a.getClass().getMethod("get" + capitalize(sortField));
                        Comparable v1 = (Comparable) m.invoke(a);
                        Comparable v2 = (Comparable) m.invoke(b);
                        return desc ? v2.compareTo(v1) : v1.compareTo(v2);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                })
                .limit(topN)
                .collect(Collectors.toList());
    }

    /**
     * 查询HQL返回单个结果
     *
     * @param hql   HQL查询语句
     * @param param 查询参数
     * @return 单个结果对象
     */
    @Transactional(readOnly = true)
    public <T> T findOne(String hql, Map<String, Object> param) {
        try {
            long startTime = System.currentTimeMillis();
            Query query = getQuery(hql, "hql", param);
            query.setFirstResult(0);
            query.setMaxResults(1);
            List<T> list = query.getResultList();
            return list.get(0);
        } catch (Exception e) {
            log.error("查询错误错误Hql:{},参数{}", hql, param, e);
            return null;
        }
    }

    @Transactional(readOnly = true)
    public <T> T findOne(Class<T> entity, String cond) {
        try {
            log.debug("根据条件查询单个实体: {}, 条件: {}", entity.getSimpleName(), cond);
            List<T> list = findList(entity, cond);
            if (list == null || list.isEmpty()) {
                return null;
            } else {
                return list.get(0);
            }
        } catch (Exception e) {
            log.error("查询错误错误:实体{},条件{}", entity, cond, e);
            return null;
        }
    }

    @Transactional(readOnly = true)
    public <T> List<T> findList(Class<T> entity, String cond) {
        try {
            log.debug("根据条件查询实体列表: {}, 条件: {}", entity.getSimpleName(), cond);
            Query query = getQuery(entity, Map.of("sql", cond));
            return query.getResultList();
        } catch (Exception e) {
            log.error("查询错误错误:实体{},条件{}", entity, cond, e);
            return null;
        }
    }

    @Override
    @Transactional(readOnly = true)
    @Cacheable(value = "countCache", key = "T(com.sys.core.server.CacheKeyBuilder).build('count', #clas.getSimpleName(), T(String).valueOf(#conds != null ? #conds.hashCode() : 0))", unless = "#result < 0")
    public <T> int getCount(Class<T> clas, Map<String, Object> conds) {
        if ("none".equalsIgnoreCase(cacheMode)) {
            return getCountNoCache(clas, conds);
        }
        String condsHash = String.valueOf(conds != null ? conds.hashCode() : 0);
        String cacheKey = CacheKeyBuilder.build("count", clas.getSimpleName(), "count", condsHash);
        return cacheServer.getOrLoad("countCache", cacheKey, () -> getCountNoCache(clas, conds));
    }

    public <T> int getCountNoCache(Class<T> clas, Map<String, Object> conds) {
        Specification<T> spec = getCriteria(conds);
        CriteriaBuilder builder = em.getCriteriaBuilder();
        CriteriaQuery<Long> query = builder.createQuery(Long.class);
        Root<T> root = applySpecificationToCriteria(clas, spec, query);
        if (query.isDistinct()) {
            query.select(builder.countDistinct(root));
        } else {
            query.select(builder.count(root));
        }
        TypedQuery<Long> typedQuery = em.createQuery(query);
        typedQuery.setHint("org.hibernate.cacheable", true);
        return typedQuery.getSingleResult().intValue();
    }

    @Transactional(readOnly = true)
    @Cacheable(value = "sumCache", key = "T(com.sys.core.server.CacheKeyBuilder).build('sum', #entity.getSimpleName(), T(String).valueOf(#param != null ? #param.hashCode() : 0), T(String).valueOf(#fields != null ? #fields.hashCode() : 0))", unless = "#result == null || #result.isEmpty()")
    public <T> List<Double> getSum(Class<T> entity, Map<String, Object> param, List<String> fields) {
        try {
            Specification<T> spec = getCriteria(param);
            CriteriaBuilder builder = em.getCriteriaBuilder();
            CriteriaQuery<Double[]> query = builder.createQuery(Double[].class);
            Root<T> root = applySpecificationToCriteria(entity, spec, query);
            List<Selection<?>> selects = new ArrayList<>();
            for (String field : fields) {
                selects.add(builder.coalesce(builder.sum(root.get(field)), builder.literal(0.0)).alias(field));
            }
            query.multiselect(selects);
            return Arrays.asList(em.createQuery(query).getResultList().get(0));
        } catch (Exception e) {
            log.error("求合错误错误:实体{},参数{},求合字段{}", entity, param, fields, e);
            return ListUtil.empty();
        }
    }

    public <T> JpaEntityInformation<T, ?> getMetadata(Class<T> domainClass) {
        Metamodel metamodel = em.getMetamodel();
        PersistenceUnitUtil util = em.getEntityManagerFactory().getPersistenceUnitUtil();
        if (Persistable.class.isAssignableFrom(domainClass)) {
            return new JpaPersistableEntityInformation(domainClass, metamodel, util);
        }
        return new JpaMetamodelEntityInformation(domainClass, metamodel, util);
    }

    private Sort getSort(Map<String, Object> param) {
        if (null != param && param.containsKey("order")) {
            List<Sort.Order> sorts = new ArrayList<Sort.Order>();
            Map<String, List> order = (Map) param.get("order");
            for (String by : order.keySet()) {
                if (by.equals("asc") || by.equals("desc")) {
                    List<String> desc = (List) order.get(by);
                    for (int i = 0; i < desc.size(); i++) {
                        sorts.add(new Sort.Order(by.equals("desc") ? Sort.Direction.DESC : Sort.Direction.ASC, desc.get(i)));
                    }
                }
            }
            return Sort.by(sorts);
        }
        return null;
    }

    private <T> Specification<T> getCriteria(Map<String, Object> param) {
        Criteria criteria = new Criteria();
        if (param.containsKey("sql")) {
            criteria.add(Restrictions.sql("id", "<sql>" + param.get("sql"), false));
        }
        param.forEach((k, v) -> {
            if (StrUtil.equalsAny(k, "or")) {
                Map<String, List> or = (Map<String, List>) v;
                List<Criterion> ors = new ArrayList<>();
                or.forEach((f, v1) -> {
                    v1.forEach(e -> {
                        ors.add(new SimpleExpression(f, e, Utils.getOperator("eq")));
                    });
                });
                criteria.add(new LogicalExpression(ors.toArray(new SimpleExpression[ors.size()]), Criterion.Operator.OR));
            } else if (StrUtil.containsAny(k, "eq", "le", "ge", "like", "in", "between")) {
                Map<String, Object> list = (Map<String, Object>) v;
                list.forEach((f, v1) -> {
                    criteria.add(new SimpleExpression(f, v1, Utils.getOperator(k)));
                });
            }
        });
        return criteria;
    }


    private <T> Query getQuery(String sql, String type, Map<String, Object> param) {
        Query query = type.equals("hql") ? em.createQuery(sql) : em.createNativeQuery(sql);
        Optional.ofNullable(param).ifPresent(e -> {
            Pattern p = Pattern.compile(":([\\w.]*)");
            Matcher m = p.matcher(sql);
            while (m.find()) {
                String name = m.group(1);
                if (param.containsKey(name)) {
                    Optional.ofNullable(param.get(name)).ifPresent(value -> query.setParameter(name, value));
                }
            }
        });
        return query;
    }

    public <T> TypedQuery getQuery(Class<T> cls, Map<String, Object> conds) {
        CriteriaBuilder builder = em.getCriteriaBuilder();
        CriteriaQuery query = builder.createQuery(cls);
        Specification<T> spec = getCriteria(conds);
        Sort sort = getSort(conds);
        Root<T> root = applySpecificationToCriteria(cls, spec, query);
        if (sort != null) {
            query.orderBy(toOrders(sort, root, builder));
        }
        query.select(root);
        TypedQuery typeQuery = em.createQuery(query);
        // typeQuery.setHint("org.hibernate.cacheable", true);
        return typeQuery;
    }

    private <S, T> Root<T> applySpecificationToCriteria(Class<T> cls, Specification<T> spec, CriteriaQuery<S> query) {
        Assert.notNull(query, "query not null");
        Root<T> root = query.from(cls);
        if (spec != null) {
            CriteriaBuilder builder = em.getCriteriaBuilder();
            Predicate predicate = spec.toPredicate(root, query, builder);
            if (predicate != null) {
                query.where(predicate);
            }
        }
        return root;
    }

    @Transactional(readOnly = true)
    public <T> T getValue(String sql, String... type) {
        return getValue(sql, null, type);
    }

    private <T> List<T> dbQuery(String sql, String type, Map<String, Object> param, String... optype) {
        try {
            Query query = getQuery(sql, type, param);
            if (optype.length > 0 && optype[0].equals("map")) {
                return query.unwrap(NativeQueryImpl.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
            }
            return query.getResultList();
        } catch (Exception e) {
            log.error("查询错误错误:{},参数{}", sql, param, e);
            return ListUtil.empty();
        }
    }

    @Transactional(readOnly = true)
    public <T> T getValue(String sql, Map<String, Object> param, String... type) {
        log.debug("查询单个值: {}, 参数: {}, 类型: {}", sql, param, type.length > 0 ? type[0] : "hql");
        List<T> datas = dbQuery(sql, type.length > 0 && type[0].equals("sql") ? "sql" : "hql", param);
        return datas.get(0);
    }

    @Transactional(readOnly = true)
    public double getCount(String sql, Map<String, Object> param, String... type) {
        log.debug("查询计数: {}, 参数: {}, 类型: {}", sql, param, type.length > 0 ? type[0] : "hql");
        return getValue(sql, param, type);
    }

    @Transactional(readOnly = true)
    public <T> T refresh(T entity, String... ignore) {
        try {
            Object id = getMetadata((Class<T>) entity.getClass()).getId(entity);
            if (id == null) {
                log.error("无法获取实体ID: {}", entity);
                return null;
            }
            // 直接从数据库加载完整实体，绕过缓存
            Class<?> entityClass = entity.getClass();
            log.debug("从数据库刷新实体: {}, ID: {}", entityClass.getSimpleName(), id);
            Map<String, Object> properties = new HashMap<>();
            properties.put("jakarta.persistence.cache.retrieveMode", CacheRetrieveMode.BYPASS);
            properties.put("jakarta.persistence.cache.storeMode", CacheStoreMode.REFRESH);
            Object refreshedEntity = em.find(entityClass, id, properties);
            return (T) refreshedEntity;
        } catch (Exception e) {
            log.error("刷新实体错误: {}", entity, e);
            return null;
        }
    }


    @Transactional(isolation = Isolation.READ_COMMITTED)
    public void insertSQL(String table, Map<String, Serializable> data) {
        StringBuilder sb = new StringBuilder();
        sb.append("INSERT INTO ").append(table).append(" (");
        data.keySet().forEach(col -> {
            sb.append(col).append(",");
        });
        sb.deleteCharAt(sb.length() - 1);
        sb.append(") VALUES (");
        data.keySet().forEach(col -> {
            sb.append(":").append(col).append(",");
        });
        sb.deleteCharAt(sb.length() - 1);
        sb.append(")");
        Query query = em.createNativeQuery(sb.toString());
        data.forEach((k, v) -> {
            query.setParameter(k, v);
        });
        query.executeUpdate();

    }

    // 批量保存方法
    @Transactional(isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    public <T> boolean batchSave(List<T> entities, int batchSize) {
        try {
            int size = entities.size();
            for (int i = 0; i < size; i++) {
                em.persist(entities.get(i));
                if (i % batchSize == 0 || i == size - 1) {
                    em.flush();
                    em.clear();
                }
            }
            return true;
        } catch (Exception e) {
            log.error("批量保存数据失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Transactional(readOnly = true)
    public <T> void processLargeResult(String hql, Consumer<T> processor) {
        try {
            Query query = em.createQuery(hql);
            // 设置流式处理
            query.getResultStream().forEach(result -> {
                try {
                    processor.accept((T) result);
                } catch (Exception e) {
                    log.error("处理结果时出错: {}", e.getMessage(), e);
                }
                // 定期清理持久化上下文
                em.detach(result);
            });
        } catch (Exception e) {
            log.error("流式查询失败: {}", e.getMessage(), e);
        }
    }

    // 使用EntityGraph控制加载策略,参数attributePathsk只返回对应的属性字段如@ManyToOne(fetch = FetchType.LAZY) private Department department;
    @Transactional(readOnly = true)
    public <T> T findWithGraph(Class<T> entityClass, Object id, String... attributePaths) {
        EntityGraph<T> graph = em.createEntityGraph(entityClass);
        for (String path : attributePaths) {
            graph.addAttributeNodes(path);
        }
        Map<String, Object> hints = new HashMap<>();
        hints.put("jakarta.persistence.fetchgraph", graph);
        return em.find(entityClass, id, hints);
    }

    /**
     * 执行分页查询指定字段
     * 内部方法，由findPage调用
     *
     * @param clas   实体类
     * @param start  起始位置
     * @param end    结束位置
     * @param conds  查询条件
     * @param fields 要查询的字段列表
     * @return 只包含指定字段的对象列表
     */
    @Transactional(readOnly = true)
    public <T> List<T> findPage(Class<T> clas, int start, int end, Map<String, Object> conds, List<String> fields) {
        try {
            fields = fields.stream().filter(field -> findField(clas, field) != null).distinct().collect(Collectors.toList());
            CriteriaBuilder cb = em.getCriteriaBuilder();
            CriteriaQuery<Tuple> query = cb.createTupleQuery();
            Root<T> root = query.from(clas);
            List<Selection<?>> selections = new ArrayList<>();
            for (String field : fields) {
                selections.add(root.get(field).alias(field));
            }
            query.multiselect(selections);
            Specification<T> spec = getCriteria(conds);
            if (spec != null) {
                Predicate predicate = spec.toPredicate(root, query, cb);
                if (predicate != null) {
                    query.where(predicate);
                }
            }
            Sort sort = getSort(conds);
            if (sort != null) {
                query.orderBy(toOrders(sort, root, cb));
            }
            TypedQuery<Tuple> typedQuery = em.createQuery(query);
            if (start >= 0) {
                typedQuery.setFirstResult(start);
                typedQuery.setMaxResults(end - start);
            }
            List<Tuple> tuples = typedQuery.getResultList();
            List<T> result = new ArrayList<>(tuples.size());
            for (Tuple tuple : tuples) {
                try {
                    T instance = clas.getDeclaredConstructor().newInstance();
                    for (String field : fields) {
                        Object value = tuple.get(field);
                        if (value != null) {
                            Field classField = findField(clas, field);
                            if (classField != null) {
                                classField.setAccessible(true);
                                classField.set(instance, value);
                            }
                        }
                    }
                    result.add(instance);
                } catch (Exception e) {
                    log.error("创建结果对象失败: {}", e.getMessage(), e);
                }
            }
            return result;
        } catch (Exception e) {
            log.error("执行分页查询失败: {}", clas.getSimpleName(), e);
            return ListUtil.empty();
        }
    }

    /**
     * 更新实体的指定字段
     *
     * @param entity 包含要更新值的实体对象
     * @param fields 要更新的字段列表
     * @return 更新是否成功
     */
    @Transactional(isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    public boolean updateFields(Object entity, List<String> fields) {
        try {
            if (entity == null || fields == null || fields.isEmpty()) {
                return false;
            }
            // 获取实体类和ID
            Class<?> entityClass = entity.getClass();
            Object id = getEntityId(entity);
            if (id == null) {
                log.error("无法获取实体ID");
                return false;
            }
            // 从数据库获取当前实体
            Object currentEntity = em.find(entityClass, id);
            if (currentEntity == null) {
                log.error("找不到ID为{}的{}实体", id, entityClass.getSimpleName());
                return false;
            }
            // 只更新指定字段
            boolean updated = false;
            for (String fieldName : fields) {
                Field field = findField(entityClass, fieldName);
                if (field != null) {
                    field.setAccessible(true);
                    Object newValue = field.get(entity);
                    Object currentValue = field.get(currentEntity);
                    if ((newValue == null && currentValue != null) ||
                            (newValue != null && !newValue.equals(currentValue))) {
                        field.set(currentEntity, newValue);
                        updated = true;
                    }
                }
            }
            if (updated) {
                em.merge(currentEntity);
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                    public void afterCommit() {
                        if (currentEntity instanceof Refresh) {
                        }
                    }
                });
            }
            return true;
        } catch (Exception e) {
            log.error("更新指定字段失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 批量更新指定字段
     *
     * @param entities 包含要更新值的实体对象列表
     * @param fields   要更新的字段列表
     * @return 更新的记录数
     */
    @Transactional(isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    public <T> int updateFieldsBatch(List<T> entities, List<String> fields) {
        try {
            if (entities == null || entities.isEmpty() || fields == null || fields.isEmpty()) {
                return 0;
            }
            int updatedCount = 0;
            for (T entity : entities) {
                if (updateFields(entity, fields)) {
                    updatedCount++;
                }
            }
            return updatedCount;
        } catch (Exception e) {
            log.error("批量更新指定字段失败: {}", e.getMessage(), e);
            return 0;
        }
    }


    /**
     * 获取实体的ID值
     */
    private Object getEntityId(Object entity) {
        try {
            JpaEntityInformation<Object, ?> metadata = getMetadata((Class<Object>) entity.getClass());
            return metadata.getId(entity);
        } catch (Exception e) {
            try {
                for (Field field : entity.getClass().getDeclaredFields()) {
                    if (field.isAnnotationPresent(Id.class)) {
                        field.setAccessible(true);
                        return field.get(entity);
                    }
                }
                try {
                    Field idField = entity.getClass().getDeclaredField("id");
                    idField.setAccessible(true);
                    return idField.get(entity);
                } catch (NoSuchFieldException ex) {
                    // 忽略
                }
            } catch (Exception ex) {
                log.error("获取实体ID失败: {}", ex.getMessage());
            }
            return null;
        }
    }

    /**
     * 查找字段（包括父类）
     */
    private Field findField(Class<?> clazz, String fieldName) {
        try {
            return clazz.getDeclaredField(fieldName);
        } catch (NoSuchFieldException e) {
            Class<?> superClass = clazz.getSuperclass();
            if (superClass != null && superClass != Object.class) {
                return findField(superClass, fieldName);
            }
            return null;
        }
    }

    public LogServer getLogServer() {
        return Optional.ofNullable(logServer).orElseGet(() -> logServer = SpringContextHolder.getBean(LogServer.class));
    }

    @Override
    @Transactional(isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class, timeout = 30)
    public boolean saveOrUpdate(Object data) {
        List<Object> list = Utils.toList(data);
        try {
            List<String> entityNames = list.stream().map(e -> e.getClass().getSimpleName()).distinct().toList();
            // 使用分片管理器执行分片保存或更新
            shardingHelper.executeWithSharding(list, entities -> {
                entities.forEach(e -> {
                    if (e instanceof Collection) {
                        saveOrUpdate(ListUtil.toList((Collection) e));
                    } else if (getMetadata((Class<Object>) e.getClass()).isNew(e)) {
                        em.persist(e);
                    } else {
                        em.merge(e);
                    }
                });
            });

            // 事务提交后处理缓存
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                public void afterCommit() {
                    cacheServer.evictAllEntityCaches(list);
                    log.debug("清除缓存: 保存或更新数据 {}", entityNames);
                    for (Object entity : list) {
                        Object id = getEntityId(entity);
                        if (id != null) {
                            cacheEventPublisher.publishEntityCacheEvictEvent(entity.getClass(), id);
                        }
                    }
                }
            });
        } catch (Exception e) {
            log.error("数据提交错误:{}", e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }
        return true;
    }

    public static <T> List<T> findTopNFromShardsAuto(
            String logicTable,
            org.springframework.data.jpa.repository.JpaRepository<T, ?> repository,
            int topN,
            String sortField,
            boolean desc
    ) {
        List<String> suffixList = Arrays.asList("1", "2", "3");
        if (suffixList.isEmpty()) {
            suffixList = Collections.singletonList("");
        }
        List<T> all = new ArrayList<>();
        Sort sort = desc ? Sort.by(Sort.Direction.DESC, sortField) : Sort.by(Sort.Direction.ASC, sortField);
        Pageable pageable = PageRequest.of(0, topN, sort);
        for (String suffix : suffixList) {
            try {
                if (!suffix.isEmpty()) {
                    TableNameContext.setSuffix(logicTable, suffix);
                } else {
                    TableNameContext.clear(); // 原表
                }
                List<T> part = repository.findAll(pageable).getContent();
                all.addAll(part);
            } finally {
                TableNameContext.clear();
            }
        }
        Comparator<T> comparator = (o1, o2) -> {
            try {
                Comparable v1 = (Comparable) o1.getClass().getMethod("get" + capitalize(sortField)).invoke(o1);
                Comparable v2 = (Comparable) o2.getClass().getMethod("get" + capitalize(sortField)).invoke(o2);
                return desc ? v2.compareTo(v1) : v1.compareTo(v2);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        };
        all.sort(comparator);
        return all.stream().limit(topN).collect(Collectors.toList());
    }

    private static String capitalize(String str) {
        if (str == null || str.isEmpty()) return str;
        return str.substring(0, 1).toUpperCase() + str.substring(1);
    }
}
