
package com.sys.core.server;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.cisdi.entity.CisdiModule;
import com.cisdi.entity.CisdiProject;
import com.cisdi.entity.CisdiTransBasecab;
import com.cisdi.entity.CisdiUserProject;
import com.sys.common.CacheUtil;
import com.sys.common.Constants;
import com.sys.common.UserInfo;
import com.sys.common.Utils;
import com.sys.core.api.BaseDao;
import com.sys.entity.Sitegroup;
import com.sys.entity.User;
import jcifs.UniAddress;
import jcifs.smb.NtlmPasswordAuthentication;
import jcifs.smb.SmbSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class UserServer {
    @Autowired
    BaseDao baseDao;

    public UserServer() {
    }

    public UserInfo getUserInfo(String userid, String password) {
        UserInfo userInfo = null;
        User user = this.getUser(userid, password);
        if (user != null) {
            (userInfo = new UserInfo(user)).setGrpList(this.getAppGroup(userid));
            userInfo.getGrpList().add("0002");
            userInfo.setSiteAuth(this.getUserSite(userid));
            userInfo.setAuthAll(this.authAll(userid));
            Map<String, Object> param = new HashMap<>();
            param.put("userid", userid);
            String hql = "from CisdiProject t where t.charge=:userid and t.proName like '%备用%'";
            CisdiProject project = baseDao.findOne(hql, param);
            if (null == project) {
                for (String proType : Arrays.asList("自动化", "传动")) {
                    for (int i = 0; i < 5; i++) {
                        project = new CisdiProject();
                        project.setCharge(userid);
                        project.setProCode(userid.toUpperCase() + "-00" + (i + 1));
                        project.setProCode(proType + "备用001");
                        project.setPlotName(proType + "备用图" + (i + 1));
                        project.setProName(proType + "备用项目");
                        project.setPlotNum(proType + "备用-" + (i + 1));
                        project.setChargeName(userid);
                        project.setProType(proType);
                        if (proType.equals("自动化")) {
                            project.setStatus(i == 0);
                            CisdiModule module = baseDao.findOne(CisdiModule.class, "module_type='电源配置图'");
                            if (null != module) {
                                project.setMacroPath(module.getMacroPath());
                            }
                            project.setTemplatePathUps(Constants.UPS_TEMPLATE_PATH);
                            project.setTemplatePathTerMina(Constants.TERMAINA_TEMPLATE_PATH);
                        } else {
                            //默认值
                            project.setSeat("MCC1");
                            project.setColorScales("RAL7035");
                            project.setProtectionGrade("IP31");
                            project.setOpxProtectionGrade("IP54");
                            project.setControlPower("是");
                            project.setRemoteIo("否");
                            project.setCabName("MCC柜");
                            project.setOpxPlotNum(project.getPlotNum());
                            project.setOpxPlotName(project.getPlotName());
                            CisdiTransBasecab transBasecab = baseDao.findOne(CisdiTransBasecab.class, " cab_type like '%固定柜%' and cab_width = '800'");
                            if (transBasecab != null) {
                                project.setBasecabId(transBasecab.getId());
                                project.setCabType(transBasecab.getCabType());
                                project.setCabSize(transBasecab.getCabSize());
                            }
                            project.setBinaryCode(true);
                        }
                        project.setTemplatePath(StrUtil.equals(proType, "auto") ? Constants.AUTO_TEMPLATE_PATH : Constants.TRANS_TEMPLATE_PATH);
                        //设置一个绑定项目
                        if (i == 0) {
                            project.setStatus(true);
                        } else {
                            project.setStatus(false);
                        }
                        baseDao.save(project);
                        CisdiUserProject userProject = new CisdiUserProject();
                        BeanUtil.copyProperties(project, userProject);
                        userProject.setUserid(user.getUserid());
                        baseDao.save(userProject);
                    }
                }
            }
            hql = "from CisdiProject where charge='" + userid + "' and status=true";
            List<CisdiProject> projectList = baseDao.findHql(hql);
            if (!projectList.isEmpty()) {
                userInfo.setProject(projectList.get(0));
            }
            Locale locale = new Locale("zh", "CN");
            Utils.addSession(org.zkoss.web.Attributes.PREFERRED_LOCALE, locale);
            Utils.addSession("userInfo", userInfo);
        }
        return userInfo;
    }

    public List<Sitegroup> getAuthSite(String hql) {
        JSONObject param;
        (param = new JSONObject()).put("userid", hql);
        hql = " from Sitegroup t where exists(select 1 from Siteuser t1 where t1.userid=:userid and t1.grpnum=t.grpnum) ";
        return this.baseDao.findHql(hql, param);
    }

    private List<String> getAppGroup(String hql) {
        JSONObject param;
        (param = new JSONObject()).put("userid", hql);
        hql = "select t.grpnum from Appgroup t where exists(select 1 from Groupuser t1 where t1.userid=:userid and t1.grpnum=t.grpnum) ";
        return this.baseDao.findHql(hql, param);
    }

    public boolean authAll(String userid) {
        return this.getAuthSite(userid).stream().filter((e) -> {
            return e.getAuthall();
        }).count() > 0L;
    }

    public List<Long> getUserSite(String hql) {
        List<Sitegroup> sites = this.getAuthSite(hql);
        JSONObject param;
        (param = new JSONObject()).put("userid", hql);
        hql = "from User t where t.userid=:userid";
        User user = (User) this.baseDao.findOne(hql, param);
        List<Long> auths = new ArrayList();
        if (sites.isEmpty()) {
            ((List) auths).add(user.getDeptId());
        } else if (!((List) (auths = CacheUtil.getAuthSite((List) sites.stream().map((e) -> {
            return e.getGrpnum();
        }).collect(Collectors.toList())))).contains(user.getDeptId())) {
            ((List) auths).add(user.getDeptId());
        }

        return (List) auths;
    }

    private User getUser(String userid, String password) {
        JSONObject param = new JSONObject();
        param.put("userid", userid);
        param.put("password", password);
        String hql = "from User t where t.userid=:userid and t.password=:password";
        User user = this.baseDao.findOne(hql, param);
        if (connect(userid, password)) {
            if (user == null) {
                hql = "from User t where t.userid=:userid";
                user = this.baseDao.findOne(hql, param);
                if (null == user) {
                    (user = new User()).setUserid(userid);
                    user.setUsername(userid);
                    user.setDeptId(12L);
                    this.baseDao.save(user);
                }
            }
        }
        return user;
    }

    private boolean connect(String username, String password) {
        try {
//            System.out.println("username-------->" + username);
//            UniAddress thisAddress = UniAddress.getByName("cisdi.com.cn");
//            NtlmPasswordAuthentication thisAuth = new NtlmPasswordAuthentication((String) System.getenv().get("USERDOMAIN"), username, password);
//            SmbSession.logon(thisAddress, thisAuth);
            return true;
        } catch (Exception var4) {
            var4.printStackTrace();
            return false;
        }
    }
}
