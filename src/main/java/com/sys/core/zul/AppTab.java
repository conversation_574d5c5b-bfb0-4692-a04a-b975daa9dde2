package com.sys.core.zul;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.Setter;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.event.Event;
import org.zkoss.zk.ui.event.Events;
import org.zkoss.zk.ui.ext.AfterCompose;
import org.zkoss.zul.*;

import java.util.List;
import java.util.stream.Collectors;

@Setter
public class AppTab extends Tab implements AfterCompose {
    TopWnd topWnd;
    boolean item2top = false;
    boolean tabClick = false;
    JSONArray toolItems = new JSONArray();

    public AppTab() {
        this.addForward("onSelect", this, "onLinkedTab");
    }

    public void onLinkedTab() {
        if (isSelected()) {
            Toolbar toolbar = getTabbox().getToolbar();
            String uuid = (String) getAttribute("uuid");
            if (null != toolbar) {
                toolbar.getChildren().stream().forEach(e -> {
                    e.setVisible(ObjectUtil.equal(e.getAttribute("uuid"), uuid));
                });
            }
            if (topWnd != null) {
                Hbox hbox = topWnd.getItemsHbox();
                List<String> uuids = getTabbox().getTabs().getChildren().stream().map(e -> (String) e.getAttribute("uuid")).collect(Collectors.toList());
                hbox.getChildren().stream().filter(e -> uuids.contains(e.getAttribute("uuid"))).forEach(e -> {
                    e.setVisible(ObjectUtil.equal(e.getAttribute("uuid"), uuid));
                });
                if (tabClick) {
                    Events.postEvent("tabClick", topWnd, this);
                }
                StrUtil.split(uuid, ",").forEach(e -> {
                    topWnd.getSubWnd().forEach(s -> {
                        if (StrUtil.equals(e, s.getUuid())) {
                            Events.postEvent("onSearch", s, this);
                        }
                    });
                });
            }
        }
    }

    private TopWnd getTopWnd(Component parent) {
        while (parent != null) {
            if (parent instanceof TopWnd) {
                return (TopWnd) parent;
            } else {
                parent = parent.getParent();
            }
        }
        return null;
    }

    public void setAddToolBar(String str) {
        if (JSONUtil.isTypeJSONArray(str)) {
            JSONArray.parseArray(str).stream().map(e -> (JSONObject) e).filter(e -> !toolItems.stream().map(d -> (JSONObject) d).map(d -> d.getString("method")).collect(Collectors.toList()).contains(e.getString("method"))).forEach(e -> {
                toolItems.add(e);
            });
        }
    }

    @Override
    public void afterCompose() {
        topWnd = getTopWnd(this.getTabbox());
    }

    @Override
    public void onCreate(Event evt) {
        super.onCreate(evt);
        if (!toolItems.isEmpty()) {
            setAttribute("uuid", getUuid());
            Toolbar toolbar = getTabbox().getToolbar();
            if (null == toolbar) {
                getTabbox().appendChild(new Toolbar());
            }
            initAppItem(toolItems);
            Events.postEvent("onSelect", getTabbox().getTabs().getFirstChild(), true);
        }
        if (ObjectUtil.equal(getTabbox().getSelectedTab(), this)) {
            Events.postEvent("onSelect", this, true);
        }
    }

    public void initAppItem(JSONArray array) {
        if (!topWnd.isSaveDisabled()) {
            Toolbar toolbar = getTabbox().getToolbar();
            array.stream().filter(e -> ObjectUtil.isNotNull(e)).map(e -> (JSONObject) e).filter(e -> e.containsKey("name") && e.containsKey("method")).forEach(e -> {
                Button button = item2top ? new Button(e.getString("name")) : new Toolbarbutton(e.getString("name"));
                button.addForward("onClick", topWnd, "onToolClick", e);
                button.setStyle("margin:3px;height:27px;font-size:13px");
                button.setAttribute("uuid", getUuid());
                button.setAttribute("wnd", topWnd.getUuid());
                button.setAttribute("json", e);
                if (item2top) {
                    button.setIconSclass(e.getString("icon"));
                    button.setStyle("margin:3px;height:27px;font-size:13px");
                    topWnd.getItemsHbox().appendChild(button);
                    topWnd.getAllButtons().put(e.getString("method"), button);
                } else {
                    button.setZclass(e.getString("icon"));
                    button.setSclass("toolbar-btn");
                    toolbar.appendChild(button);
                }
                // button.setDisabled(topWnd.saveDisabled);
            });
        }
    }

    public void setItem2top(boolean item2top) {
        this.item2top = item2top;
    }

    public void setSelectedDirectly(boolean selected) {
        super.setSelectedDirectly(selected);
        onLinkedTab();
    }
}
