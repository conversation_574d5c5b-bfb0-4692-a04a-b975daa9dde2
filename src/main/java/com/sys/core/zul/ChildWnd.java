package com.sys.core.zul;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.sys.common.Message;
import com.sys.common.Utils;
import com.sys.core.zul.api.WorkFlow;
import com.sys.entity.User;
import com.sys.entity.WfNode;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.zkoss.zk.ui.event.Events;
import org.zkoss.zul.CompTarget;
import org.zkoss.zul.Messagebox;
import org.zkoss.zul.Window;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Setter
@Getter
public class ChildWnd extends RecWnd {
    @Autowired
    protected WorkFlow flow;
    private boolean barStatus = true;
    private boolean showFlow = false;
    private boolean hasItem = false;
    private WfNode wfNode;


    @Override
    public void binderTarget(CompTarget target) {
        if (hasWf() && target != null) {
            if (FieldUtils.getField(target.getValue().getClass(), "wfinsid", true) != null) {
                Map<String, Object> nodeInfo = flow.getWfInfo(target.getValue(), getUserInfo().getUserIds(), getApp().getWfId(), getWorkApp(target));
                hasItem = (Boolean) nodeInfo.get("hasitem");
                wfNode = (WfNode) nodeInfo.get("wfnode");
                if (isSaveDisabled() && hasItem) {
                    setSaveDisabled(false);
                }
            }
        }
        super.binderTarget(target);
    }

    public String getWorkApp(CompTarget target) {
        return getApp().getAppName();
    }

    public void sendFlow() {
        if (!hasWf() || !hasItem) {
            Message.showError("您没有授权处理该流程");
            return;
        }
        if (save(false)) { // Check data integrity
            List<WfNode> nodeList = flow.nextNodes(getMainData(), wfNode);
            if (showFlow || nodeList.size() > 1) {
                showNodeSelectionWindow(nodeList);
            } else if (nodeList.size() == 1) {
                processSingleNode(nodeList.get(0));
            }
        }
    }

    private void showNodeSelectionWindow(List<WfNode> nodeList) {
        Map<String, Object> prop = new HashMap<>();
        prop.put("pnode", wfNode);
        prop.put("parentWnd", this);
        prop.put("nodelist", nodeList);
        Window window = Utils.createWindow("~./page/sys/wf/choose.zul", prop);
        if (window != null) {
            window.doModal();
        }
    }

    private void processSingleNode(WfNode node) {
        List<User> users = getNodeUser(node);
        if (node.getNodeType() == 6 || (users != null && !users.isEmpty())) {
            Map<WfNode, List<User>> nodeInfo = new HashMap<>();
            nodeInfo.put(node, users);
            if (canWorkFlow(getMainData(), wfNode, node)) {
                List<String> wfUsers = flow.sendFlow(getMainData(), getUserInfo().getUserIds(), wfNode, nodeInfo, "同意", 0, createItem(wfNode, node));
                flowSuccess(wfNode, nodeInfo, wfUsers);
                Optional.ofNullable(getParentWnd()).ifPresent(e -> Events.postEvent("onSearch", e, null));
                detach();
            }
        } else {
            Message.showError("请配置流程人员");
        }
    }

    public boolean canWorkFlow(Object main, WfNode cur, WfNode next) {
        return true;
    }

    public boolean createItem(WfNode cur, WfNode next) {
        if (cur.getMoreuser()) { // Multi-user processing
            Long wfinsid = (Long) BeanUtil.getFieldValue(getMainData(), "wfinsid");
            String sql = "select count(1) from s_wf_workitem t where t.wfinsid=" + wfinsid + " and t.userid<>'" + getUser().getUserId() + "' and exists(select 1 from s_wf_work_step t1 where t.wfinsid=t1.wfinsid and t1.target=" + cur.getNodeId() + " and t.nodeid=t1.source)";
            return baseDao.getCount(sql, null, "sql") == 0;
        }
        return true;
    }

    public void flowSuccess(WfNode node, Map<WfNode, List<User>> nodeInfo, List<String> users) {
    }

    public List<User> getNodeUser(WfNode node) {
        return flow.getNodeUser(getMainData(), getUserInfo().getSiteAuth(), node, wfNode);
    }

    public void rollbackFlow() {
        if (!hasWf()) {
            Message.showError("您没有授权处理该流程");
            return;
        }
        if (!flow.recoverFlow(getMainData(), getUserInfo().getUserIds(), getUserInfo().getSiteAuth())) {
            List<WfNode> nodes = flow.getBackNode(getMainData(), getUserInfo().getUserIds());
            if (ObjectUtil.isNotEmpty(nodes)) {
                showRollbackWindow(nodes);
            } else {
                Message.showError("您没有权限处理该流程");
            }
        } else {
            binderTarget(getTarget());
            Message.showInfo("回收流程成功");
        }
    }

    private void showRollbackWindow(List<WfNode> nodes) {
        Map<String, Object> prop = new HashMap<>();
        prop.put("nodelist", nodes);
        prop.put("parentWnd", this);
        Window window = Utils.createWindow("~./page/sys/wf/back.zul", prop);
        if (window != null) {
            window.doModal();
            binderTarget(getTarget());
        }
    }

    public void terminateFlow() {
        if (hasWf()) {
            Messagebox.show("您确定要终止流程", "询问", Messagebox.YES | Messagebox.NO, Messagebox.QUESTION, ent -> {
                if (StrUtil.equals(ent.getName(), Messagebox.ON_YES)) {
                    if (flow.terminateFlow(getMainData(), wfNode, getUserInfo().getUserIds())) {
                        binderTarget(getTarget());
                        Message.showInfo("终止流程成功");
                    }
                }
            });
        } else {
            Message.showError("您没有授权处理该流程");
        }
    }

    public void viewFlow() {
        if (hasWf()) {
            Utils.openData("~./page/sys/wf/view.zul", getTarget(), this);
        }
    }
}
