package com.sys.core.zul;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.sys.common.CacheUtil;
import com.sys.common.Constants;
import com.sys.common.Message;
import com.sys.common.Utils;
import com.sys.core.zul.api.EnumType;
import com.sys.core.zul.api.EnumType.ITEM_STYLE;
import com.sys.core.zul.api.GridConfig;
import com.sys.core.zul.api.TableData;
import com.sys.entity.Appconfig;
import com.sys.entity.ButtonCfg;
import com.sys.entity.Document;
import io.keikai.api.AreaRef;
import io.keikai.api.Exporters;
import io.keikai.api.model.Book;
import io.keikai.api.model.Sheet;
import io.keikai.model.SRow;
import io.keikai.model.SSheet;
import io.keikai.ui.*;
import io.keikai.ui.event.CellEvent;
import io.keikai.ui.event.CellHyperlinkEvent;
import io.keikai.ui.event.StopEditingEvent;
import io.keikai.ui.impl.DefaultUserActionManagerCtrl;
import io.keikai.ui.sys.SpreadsheetInCtrl;
import lombok.Getter;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.event.Event;
import org.zkoss.zk.ui.event.Events;
import org.zkoss.zul.Filedownload;
import org.zkoss.zul.Listitem;
import org.zkoss.zul.Messagebox;

import java.beans.PropertyDescriptor;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;

public class ExcelTable extends Spreadsheet implements TableData {
    List<Appconfig> cfgList;
    GridConfig gridWnd;
    String src = "web/page/sys/blank.xlsx";
    @Getter
    Map<Integer, String> sheetMap;
    SSheet sheet;
    String groupCol;

    public ExcelTable(GridConfig gridWnd) {
        setHflex("1");
        setVflex("1");
        setShowToolbar(true);
        setShowFormulabar(true);
        setShowSheetbar(gridWnd.isCurExcel());
        if (gridWnd.isCurExcel()) {
            removeToolbarButton(AuxAction.NEW_BOOK);
            removeToolbarButton(AuxAction.SAVE_BOOK);
            removeToolbarButton(AuxAction.CLOSE_BOOK);
            removeToolbarButton(AuxAction.EXPORT_PDF);
            removeToolbarButton(AuxAction.PIE_CHART);
            removeToolbarButton(AuxAction.PIE_CHART_3D);
            removeToolbarButton(AuxAction.INSERT_PICTURE);
            removeToolbarButton(AuxAction.HYPERLINK);
            removeToolbarButton(AuxAction.INSERT_CHART);
            removeToolbarButton(AuxAction.ADD_SHEET);
            removeToolbarButton(AuxAction.DELETE_SHEET);
            removeToolbarButton(AuxAction.COPY_SHEET);
            removeToolbarButton(AuxAction.RENAME_SHEET);
            removeToolbarButton(AuxAction.PROTECT_SHEET);
            //removeToolbarButton(AuxAction.GRIDLINES);
            disableUserAction(AuxAction.ADD_SHEET, true);
            UserActionManager uam = getUserActionManager();
            if (Utils.getUserInfo().getGrpList().contains("1000")&& Constants.showCfg) {
                addToolbarButton(new ConfigToolBar("config", "k-icon-newBook"));
                addToolbarButton(new ConfigToolBar("show", "k-icon-saveBook"));
                uam.setHandler(DefaultUserActionManagerCtrl.Category.AUXACTION.getName(), "config",
                        new UserActionHandler() {
                            @Override
                            public boolean process(UserActionContext ctx) {
                                Events.postEvent("onShowCfg", gridWnd, null);
                                return true;
                            }

                            @Override
                            public boolean isEnabled(Book book, Sheet sheet) {
                                return true;
                            }
                        });

                uam.setHandler(DefaultUserActionManagerCtrl.Category.AUXACTION.getName(), "show",
                        new UserActionHandler() {
                            @Override
                            public boolean process(UserActionContext ctx) {
                                Events.postEvent("onAllCfg", gridWnd, null);
                                return true;
                            }

                            @Override
                            public boolean isEnabled(Book book, Sheet sheet) {
                                return true;
                            }
                        });
            } else {
                addToolbarButton(new ConfigToolBar("show", "z-icon-arrows-alt"));
                uam.setHandler(DefaultUserActionManagerCtrl.Category.AUXACTION.getName(), "show",
                        new UserActionHandler() {
                            @Override
                            public boolean process(UserActionContext ctx) {
                                Events.postEvent("onViewCfg", gridWnd, ExcelTable.this);
                                return true;
                            }

                            @Override
                            public boolean isEnabled(Book book, Sheet sheet) {
                                return true;
                            }
                        });
            }
        }
        setSrc(src);
        setShowContextMenu(true);
        sheet = getSelectedSSheet();
        sheetMap = sheet.initHeader(this.cfgList = (this.gridWnd = gridWnd).getConfigList());
        setMaxVisibleColumns(cfgList.size());
    }

    public void hideHeader(List<String> hideList) {
        if (null != hideList) {
            SpreadsheetInCtrl ctrl = (SpreadsheetInCtrl) getExtraCtrl();
            String regEx = "([0-9]+[.]?[0-9]*)";
            cfgList.forEach(e -> {
                sheetMap.forEach((k, v) -> {
                    if (StrUtil.equals(e.getDispName(), v)) {
                        int width = hideList.contains(v) ? 0 : StrUtil.containsAnyIgnoreCase(e.getWidth(), "px") ? Integer.parseInt(ReUtil.get(regEx, e.getWidth(), 0)) : 150;
                        ctrl.setColumnSize(sheet.getId(), k, width, k, false);
                    }
                });
            });
            invalidate();
        }
    }

    @Override
    public <T> void add(T data) {
        sheet.addSheetData(data);
        setMaxVisibleRows(sheet.getEndRowIndex() + 40);
        invalidate();
    }

    public <T> void addAll(List<T> data) {
        sheet.addSheetData(data);
        setMaxVisibleRows(sheet.getEndRowIndex() + 40);
        invalidate();
    }

    @Override
    public void abandon(Object data) {

    }

    public Object getBinderData(Component comp) {
        return null;
    }

    public <T> void setDatas(List<T> datas) {
        try {
            sheet.setSheetData(datas);
            setMaxVisibleRows(datas.size() + 40);
            invalidate();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void initTable(List<Appconfig> cfgList) {

    }


    public void reLoadData(Object data) {
        sheet.refreshSheet(data);
    }

    @Override
    public List<Object> getSelectData() {
        return sheet.getSheetData(getSelection().getRow(), getSelection().getLastRow());
    }


    public void deleteRow(SSheet ssheet, SRow row) {
        if (null != row && gridWnd instanceof PageWnd) {
            PageWnd pageWnd = (PageWnd) gridWnd;
            pageWnd.update.get(EnumType.UPDATE_TYPE.DEL).add(row.getData());
        }
    }

    public void postChange(Object data, String field, Object value) {
        Sheet sheet = getSelectedSheet();
        SRow row = sheet.getInternalSheet().getRow(data);
        if (null != row) {
            BeanUtil.setFieldValue(data, field, value);
            int inx = sheetMap.values().stream().toList().indexOf(field);
            if (inx != -1) {
                int col = sheetMap.keySet().stream().toList().get(inx);
                CellEvent event = new StopEditingEvent("onStopEditing", this, sheet, row.getIndex(), col, Convert.toStr(value, ""));
                Events.postEvent(event);
                Events.postEvent(new Event("onStopEditingImpl", this, new Object[]{"", event, "inlineEditing"}));
            }
        }
    }

    public void stopEditing(StopEditingEvent event) {
        int col = event.getColumn();
        if (!sheetMap.containsKey(col) || StrUtil.endWith(sheetMap.get(col), "_btn") || !canChange(event.getSheet().getInternalSheet().getRow(event.getRow()).getData(), sheetMap.get(col), event.getEditingValue())) {
            event.cancel();
        }
    }

    public boolean canChange(Object data, String filed, Object value) {
        return true;
    }

    public void paste(SSheet sheet, List<List<Object>> values, AreaRef area) {
        try {
            int inx = 0, col;
            int size = values.size();
            for (int i = area.getRow(); i <= area.getLastRow(); i++) {
                List<Object> value = values.get(inx % size);
                if (value.stream().anyMatch(ObjectUtil::isNotEmpty)) {
                    col = 0;
                    SRow srow = sheet.getRow(i);
                    Object data = srow.getData();
                    PageWnd pageWnd = (PageWnd) gridWnd;
                    boolean add = data == null;
                    if (null == data) {
                        data = pageWnd.formatNewObject(pageWnd.getNewObject());
                        PropertyDescriptor proper = BeanUtil.getPropertyDescriptor(data.getClass(), "sortNo");
                        if (proper != null) {
                            Number number;
                            Object prvData = sheet.getRow(i - 1).getData();
                            if (prvData != null && (number = (Number) BeanUtil.getFieldValue(prvData, "sortNo")) != null) {
                                BeanUtil.setFieldValue(data, "sortNo", (number.longValue() + i * 30L) / 2);
                            } else {
                                BeanUtil.setFieldValue(data, "sortNo", i * 30);
                            }
                        }
                        srow.setData(data);
                    }
                    for (int j = area.getColumn(); j <= area.getLastColumn(); j++) {
                        String colName = sheetMap.get(j);
                        if (StrUtil.isNotBlank(colName)) {
                            BeanUtil.setFieldValue(data, colName, value.get(col));
                        }
                        col++;
                    }
                    if (pageWnd.changeSave) {
                        pageWnd.saveBefore(data);
                        if (add) {
                            pageWnd.baseDao.save(data);
                        } else {
                            pageWnd.baseDao.update(data);
                        }
                    } else if (add) {
                        pageWnd.update.get(EnumType.UPDATE_TYPE.ADD).add(data);
                    } else if (!pageWnd.update.get(EnumType.UPDATE_TYPE.ADD).contains(data)) {
                        pageWnd.update.get(EnumType.UPDATE_TYPE.UPDATE).add(data);
                    }
                }
                inx++;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public void cellEditTex(SSheet ssheet, int rowIdx, int colIdx) {
        super.cellEditTex(sheet, rowIdx, colIdx);
        try {
            String colName = sheetMap.get(colIdx);
            SRow srow = ssheet.getRow(rowIdx);
            Object data = srow.getData();
            Object value = ssheet.getCell(rowIdx, colIdx).getValue();
            if (!ObjectUtil.isAllEmpty(data, value)) {
                PageWnd pageWnd = (PageWnd) gridWnd;
                boolean add = data == null;
                if (null == data) {
                    data = pageWnd.formatNewObject(pageWnd.getNewObject());
                    PropertyDescriptor proper = BeanUtil.getPropertyDescriptor(data.getClass(), "sortNo");
                    if (proper != null) {
                        Number number;
                        Object prvData = sheet.getRow(rowIdx - 1).getData();
                        if (prvData != null && (number = (Number) BeanUtil.getFieldValue(prvData, "sortNo")) != null) {
                            BeanUtil.setFieldValue(data, "sortNo", (number.longValue() + rowIdx * 30L) / 2);
                        } else {
                            BeanUtil.setFieldValue(data, "sortNo", rowIdx * 30);
                        }
                    }
                    srow.setData(data);
                }
                BeanUtil.setFieldValue(data, colName, value);
                Object orgVal = BeanUtil.getFieldValue(data, colName);
                if (pageWnd.changeSave) {
                    pageWnd.saveBefore(data);
                    if (add) {
                        pageWnd.baseDao.save(data);
                    } else {
                        pageWnd.baseDao.update(data);
                    }
                } else {
                    if (add) {
                        pageWnd.update.get(EnumType.UPDATE_TYPE.ADD).add(data);
                    } else if (!pageWnd.update.get(EnumType.UPDATE_TYPE.ADD).contains(data)) {
                        pageWnd.update.get(EnumType.UPDATE_TYPE.UPDATE).add(data);
                    }
                    pageWnd.change(data, colName, orgVal, value);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

//    public void onDelete() {
//        if (Message.showQuestion("确定要删除") == Messagebox.YES) {
//            ((PageWnd) gridWnd).del();
//            Ranges.range(getSelectedSheet(), getSelection()).toRowRange().delete(Range.DeleteShift.UP);
//        }
//    }

    public void doExportBook() {
        Book loadedBook = this.getBook();
        try {
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            Exporters.getExporter().export(loadedBook, out);
            String encodedName = gridWnd.getMainTable().getTabDes() + ".xlsx";
            Filedownload.save(out.toByteArray(), "application/octet-stream", encodedName);
        } catch (IOException e) {
            Message.showError("导出失败");
        }
    }


    @Override
    public <T> List<T> getTableData() {
        return getSelectedSSheet().getSheetData();
    }

    public void itemStyle(Object data, ITEM_STYLE style) {
        if (style == ITEM_STYLE.item_del) {
            getSelectedSSheet().remove(data);
        }
    }

    @Override
    public void remove(Object data) {

    }

    class ConfigToolBar extends ToolbarButton {
        String key;
        String icon;

        public ConfigToolBar(String key, String icon) {
            super(key);
            this.key = key;
            this.icon = icon;
        }


        @Override
        public String toJSONString() {
            JSONObject map = new JSONObject();
            map.put("key", key);
            map.put("iconClass", icon);
            return map.toString();
        }
    }

    @Override
    public void hide(String dispName, boolean hide) {
        String regEx = "([0-9]+[.]?[0-9]*)";
        SpreadsheetInCtrl ctrl = (SpreadsheetInCtrl) getExtraCtrl();
        Appconfig cfg = cfgList.stream().filter(e -> e.getDispName().equals(dispName)).findFirst().orElse(null);
        if (null != cfg) {
            sheetMap.forEach((k, v) -> {
                if (StrUtil.equals(v, dispName)) {
                    int width = hide ? 0 : StrUtil.containsAnyIgnoreCase(cfg.getWidth(), "px") ? Integer.parseInt(ReUtil.get(regEx, cfg.getWidth(), 0)) : 150;
                    ctrl.setColumnSize(sheet.getId(), k, width, k, false);
                }
            });
            invalidate();
        }
    }

    @Override
    public List<String> getSumCols() {
        return ListUtil.empty();
    }

    @Override
    public void setSumValues(List<Object> valueList) {

    }

    @Override
    public boolean canDelete(SSheet sheet, List<SRow> rows) {
        if (Message.showQuestion("请确认要删除") == Messagebox.YES) {
            return true;
        }
        return false;
    }

    public void onCellHyperlink(CellHyperlinkEvent event) {
        SSheet sheet = event.getSheet().getInternalSheet();
        String field = sheetMap.get(event.getColumn());
        Object main = sheet.getRow(event.getRow()).getData();
        if (StrUtil.endWith(field, "_btn")) {
            Map<String, Object> param = new HashMap<>();
            param.put("target", new Listitem(main));
            ButtonCfg cfg = (ButtonCfg) sheet.getAttribute(field);
            String method = cfg.getMethodName();
            Map<String, List> pages = CacheUtil.getPageConfig(cfg);
            if (pages.isEmpty()) {
                String[] methods = method.split("=");
                param.put("method", methods[0]);
                if (StrUtil.startWith(method, "openData")) {
                    param.put("page", methods.length > 1 ? methods[1] : gridWnd.getApp().getPage());
                }
                Events.postEvent((String) param.get("method"), gridWnd, param);
            } else {
                param.put("parentWnd", gridWnd);
                param.put("app", gridWnd.getApp());
                String content = Utils.getPageConfig(cfg, pages);
                if (StrUtil.isNotBlank(content)) {
                    Utils.openData(param, content);
                }
            }
        } else {//文件下载
            String saveName = (String) BeanUtil.getFieldValue(main, field);
            if (StrUtil.isNotBlank(saveName)) {
                Document document = gridWnd.getBaseDao().find(Document.class, saveName);
                if (null != document) {
                    Filedownload.save(document.getFilepath(), "application/octet-stream", document.getFilename());
                }
            }
        }
    }

    public void autoFitColumnWidth(SSheet sheet, int col, int width) {
        if (null != cfgList) {
            Appconfig cfg = cfgList.get(col);
            if (null != cfg) {
                cfg.setWidth(String.format("%spx", width + 15));
                gridWnd.getBaseDao().update(cfg);
            }
        }
    }

    public void loadDataAfter(SSheet sheet) {
        if (gridWnd instanceof GridWnd) {
            ((GridWnd) gridWnd).loadDataAfter();
        }
    }

    @Override
    public void setGroupCol(String groupCol) {
        this.groupCol = groupCol;
    }
}
