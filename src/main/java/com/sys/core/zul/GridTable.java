package com.sys.core.zul;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.sys.common.Utils;
import com.sys.core.zul.api.EnumType.ITEM_STYLE;
import com.sys.core.zul.api.GridConfig;
import com.sys.core.zul.api.RelationComp;
import com.sys.core.zul.api.TableData;
import com.sys.entity.AppConfig;
import com.sys.entity.Require;
import lombok.Getter;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.zkoss.zk.ui.AbstractComponent;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.event.Event;
import org.zkoss.zk.ui.event.Events;
import org.zkoss.zk.ui.event.ForwardEvent;
import org.zkoss.zkplus.databind.AnnotateDataBinder;
import org.zkoss.zkplus.databind.DataBinder;
import org.zkoss.zkplus.databind.TypeConverter;
import org.zkoss.zul.*;
import org.zkoss.zul.event.ColSizeEvent;
import org.zkoss.zul.impl.InputElement;
import org.zkoss.zul.impl.LabelElement;

import java.util.*;
import java.util.stream.Collectors;

@Getter
@Setter
public class GridTable extends Listbox implements ListitemRenderer, TypeConverter, TableData {
    private static final Logger log = LoggerFactory.getLogger(GridTable.class);
    private List<AppConfig> cfgList;
    private GridConfig gridWnd;
    private DataBinder dataBinder;
    private TableModel modelList;
    private GroupsModelArray modelArray;
    private Listfoot listfoot;
    private List<Object> tableData = new ArrayList<>();
    private int rowNum;
    private String fcol;
    private boolean hasEdit = false;
    private List<Header> headers = new ArrayList<>();
    private List<Require> requires;
    private String groupCol;
    private boolean loadComp = false;
    private List<String> butCol = new ArrayList<>();
    private List<String> sumCols = new ArrayList<>();
    private int firstSum = -1;

    public GridTable(GridConfig gridWnd) {
        setVflex("1");
        this.gridWnd = gridWnd;
        this.requires = gridWnd.getRequires();
        this.groupCol = gridWnd.getGroupCol();
        setItemRenderer(this);
        if (groupCol == null) {
            setModel(modelList = new TableModel());
        }
        this.cfgList = gridWnd.getConfigList();
        initTable(this.cfgList);
        dataBinder = new AnnotateDataBinder(gridWnd, true);
        setCheckmark(gridWnd.multiple());
        setMultiple(gridWnd.multiple());
        addForward("onCheckSelectAll", gridWnd, "onCheckSelectAll");
    }

    @Override
    public <T> void add(T data) {
        if (data == null) return;
        tableData.add(data);
        if (groupCol != null) {
            setModel(modelArray = new TableGroupModel(tableData, new GridGroup(groupCol), gridWnd.multiple()));
        } else {
            modelList.add(data);
        }
    }

    public <T> void addAll(List<T> data) {
        if (data == null || data.isEmpty()) return;
        tableData.addAll(data);
        if (groupCol != null) {
            setModel(modelArray = new TableGroupModel(tableData, new GridGroup(groupCol), gridWnd.multiple()));
        } else {
            modelList.addAll(data);
        }
    }

    @Override
    public void itemStyle(Object data, ITEM_STYLE style) {
        if (data == null) return;
        getItems().stream().filter(item -> !(item instanceof Listgroup)).filter(item -> ObjectUtil.equal(data, item.getValue())).forEach(item -> item.setSclass(style == null ? null : style.name()));
    }

    @Override
    public <T> List<T> getSelectData() {
        return (List<T>) getSelectedItems().stream().map(Listitem::getValue).collect(Collectors.toList());
    }

    @Override
    public <T> void remove(T data) {
        if (data == null) return;
        List<T> itemsToRemove = Utils.toList(data);
        tableData.removeAll(itemsToRemove);
        if (groupCol != null) {
            setModel(modelArray = new TableGroupModel(tableData, new GridGroup(groupCol), true));
        } else {
            modelList.removeAll(itemsToRemove);
        }
    }

    public void initTable(List<AppConfig> configList) {
        final Listhead head = createTableHead(configList);
        appendChild(head);
        if (gridWnd.getFrozen() > 0) {
            Frozen frozen = new Frozen();
            frozen.setColumns(gridWnd.getFrozen());
            appendChild(frozen);
        }
        createFooterIfNeeded(configList);
        List<AppConfig> loadedConfigs = new ArrayList<>(head.getChildren().size());
        headers.forEach(header -> loadedConfigs.add(header.getCfg()));
        cfgList.clear();
        cfgList.addAll(loadedConfigs);
        AppConfig sumConfig = cfgList.stream()
                .filter(cfg -> sumCols.contains(cfg.getDispName()))
                .findFirst()
                .orElse(null);
        firstSum = sumConfig != null ? cfgList.indexOf(sumConfig) : -1;
        head.addForward("onColSize", this, "onColSize", head);
        Events.postEvent("initHeader", gridWnd, this);
    }

    private Listhead createTableHead(List<AppConfig> configList) {
        final Listhead head = new Listhead();
        Listheader indexHeader = new Listheader(gridWnd.getFname());
        indexHeader.setWidth(gridWnd.getFnameWidth());
        indexHeader.setAlign(StrUtil.isNotBlank(groupCol) && gridWnd.multiple() ? "left" : "center");
        head.appendChild(indexHeader);
        head.setSizable(true);
        List<AppConfig> loadCfgList = new ArrayList<>();
        String defaultWidth = configList.size() > 10 ? "100px" : "auto";
        if (hasParentHeaders(configList)) {
            createMultiLevelHeaders(head, configList, defaultWidth, loadCfgList);
        } else {
            head.setMenupopup("auto");
            head.setColumnsgroup(true);
            addHeadersToHead(head, configList, defaultWidth, loadCfgList, false);
        }
        return head;
    }

    private boolean hasParentHeaders(List<AppConfig> configList) {
        return configList.stream().anyMatch(cfg -> StrUtil.isNotBlank(cfg.getPcfgDes()));
    }

    private void createMultiLevelHeaders(Listhead head, List<AppConfig> configList, String defaultWidth, List<AppConfig> loadCfgList) {
        configList.stream().filter(cfg -> StrUtil.isBlank(cfg.getPcfgDes())).forEach(cfg -> cfg.setPcfgDes(cfg.getCfgDes()));
        Auxhead auxhead = new Auxhead();
        Auxheader indexAuxHeader = new Auxheader("序号");
        auxhead.appendChild(indexAuxHeader);
        indexAuxHeader.setAlign("center");
        indexAuxHeader.setRowspan(2);
        Map<String, List<AppConfig>> groupedConfigs = configList.stream().sorted(Comparator.comparing(AppConfig::getSort, Comparator.nullsLast(Comparable::compareTo))).collect(Collectors.groupingBy(AppConfig::getPcfgDes, LinkedHashMap::new, Collectors.toList()));
        groupedConfigs.forEach((headerText, configs) -> {
            Auxheader parentHeader = new Auxheader(headerText);
            auxhead.appendChild(parentHeader);
            parentHeader.setAlign("center");
            if (configs.size() == 1) {
                parentHeader.setRowspan(2);
            } else {
                parentHeader.setColspan(configs.size());
            }
            addHeadersToHead(head, configs, defaultWidth, loadCfgList, true);
        });
        appendChild(auxhead);
        hideOverlappingHeaders(head, auxhead);
    }

    private void hideOverlappingHeaders(Listhead head, Auxhead auxhead) {
        List<String> auxHeaderLabels = auxhead.getChildren().stream().map(comp -> (Auxheader) comp).map(LabelElement::getLabel).collect(Collectors.toList());
        head.getChildren().stream().map(comp -> (Listheader) comp).filter(header -> auxHeaderLabels.contains(header.getLabel())).forEach(header -> header.setStyle("display: none;"));
    }

    private void addHeadersToHead(Listhead head, List<AppConfig> configList, String defaultWidth, List<AppConfig> loadCfgList, boolean isAuxChild) {
        for (AppConfig config : configList) {
            Header header = new Header(config, gridWnd);
            head.appendChild(header);
            header.setSort("auto");
            header.setWidth(getHeaderWidth(config, defaultWidth));
            header.setRequire(isRequiredField(config.getDispName()));
            hasEdit = hasEdit || config.isEdit();
            headers.add(header);
            if (config.isHide()) {
                header.setVisible(false);
            }
            if (isAuxChild) {
                header.setSclass("aux-header");
            }
            loadCfgList.add(config);
        }
    }

    private String getHeaderWidth(AppConfig config, String defaultWidth) {
        return StrUtil.containsAnyIgnoreCase(config.getWidth(), "px", "auto", "%") ? config.getWidth() : defaultWidth;
    }

    private boolean isRequiredField(String fieldName) {
        return requires.stream().filter(req -> StrUtil.equals(fieldName, req.getDispName())).anyMatch(Require::isRequire);
    }

    private void createFooterIfNeeded(List<AppConfig> configList) {
        boolean needFooter = configList.stream().filter(cfg -> StrUtil.containsAnyIgnoreCase(cfg.getHbType(), "double", "int", "long")).anyMatch(cfg -> (cfg.getCfgAuth() & 2) == 2);
        if (!needFooter) return;
        listfoot = new Listfoot();
        appendChild(listfoot);
        Listfooter totalLabel = new Listfooter("合计:");
        listfoot.appendChild(totalLabel);
        for (AppConfig config : configList) {
            Listfooter footer = new Listfooter();
            listfoot.appendChild(footer);
            if (StrUtil.containsAnyIgnoreCase(config.getHbType(), "double", "int", "long") && (config.getCfgAuth() & 2) == 2) {
                footer.setAttribute("dispName", config.getDispName());
                sumCols.add(config.getDispName());
            }
        }
    }

    public void onColSize(Event event) {
        Listhead head = (Listhead) event.getData();
        ForwardEvent forward = (ForwardEvent) event;
        ColSizeEvent colEvent = (ColSizeEvent) forward.getOrigin();
        colEvent.getColumn().setVisible(true);
        head.invalidate();
    }

    public void setRequires(List<Require> requireList) {
        if (requireList == null) return;
        headers.forEach(header -> {
            boolean isRequired = requireList.stream().anyMatch(req -> req.isRequire() && StrUtil.equals(req.getDispName(), header.getCfg().getDispName()));
            header.setRequire(isRequired);
        });
    }

    public Object coerceToBean(Object o, Component component) {
        return Optional.of(!ObjectUtil.equal(o, "")).filter(Boolean.TRUE::equals).map(e -> o).orElse(null);
    }

    @Override
    public Object coerceToUi(Object o, Component component) {
        if (component instanceof RelationComp comp && StrUtil.isAllNotEmpty(comp.getTarget(), comp.getTargetCond(), comp.getTargetCol())) {
            String sql = Utils.formatSql(comp.getTargetCond(), getBinderData(component));
            return Optional.ofNullable(Optional.ofNullable(gridWnd.getCacheServer().getTimedCache(sql)).orElseGet(() -> gridWnd.getCacheServer().putTimedCache(sql, gridWnd.getBaseDao().findOne(Utils.getDbClass(gridWnd.getCacheServer().getMainTable(comp.getTarget())), sql)))).map(data -> BeanUtil.getFieldValue(data, comp.getTargetCol())).orElse(o);
        }
        return o;
    }

    public Object getBinderData(Component comp) {
        if (dataBinder == null || comp == null) return null;
        return Optional.ofNullable(dataBinder.getBinding(comp, "value")).map(binding -> {
            String[] parts = binding.getExpression().split("\\.");
            return parts.length > 0 ? dataBinder.getBean(parts[0]) : null;
        }).orElse(null);
    }

    @Override
    public void abandon(Object data) {
        if (data == null) return;
        Listitem item = getListitem(data);
        if (item == null) return;
        item.setSclass(null);
        Object refreshedData = gridWnd.getBaseDao().refresh(data);
        item.setValue(refreshedData);
        dataBinder.bindBean(item.getUuid(), refreshedData);
        dataBinder.loadComponent(item);
    }

    @Override
    public void reLoadData(Object data) {
        if (data == null || cfgList == null) return;
        Listitem item = getListitem(data);
        if (item == null) return;
        dataBinder.bindBean(item.getUuid(), data);
        item.getChildren().stream().filter(cell -> cell.getFirstChild() instanceof Combobox).map(cell -> (Combobox) cell.getFirstChild()).filter(combo -> combo.getApplist() != null).forEach(Combobox::initChildren);
        dataBinder.loadComponent(item);
    }

    public void reloadAll(Object data) {
        if (data == null || cfgList == null) return;
        Listitem item = getListitem(data);
        if (item == null) return;
        item.getChildren().subList(1, item.getChildren().size()).clear();
        cfgList.forEach(config -> {
            try {
                item.appendChild(new TableCell(getDataBinder(), config, data, gridWnd, this, item));
            } catch (Exception ex) {
                log.error("创建表格单元格失败: {}", ex.getMessage(), ex);
            }
        });
        dataBinder.loadComponent(item);
        Events.postEvent("onRender", gridWnd, item);
    }

    public <T> void sortGroupData(List<T> datas) {
        if (datas == null) return;
        tableData.clear();
        tableData.addAll(datas);
        if (modelArray == null) return;
        modelArray.setGroupData(datas.toArray());
        if (gridWnd instanceof GridWnd) {
            Button groupButton = ((GridWnd) gridWnd).getSearch().getGroup();
            if (groupButton != null) {
                boolean shouldExpand = "分组".equals(groupButton.getLabel());
                getGroups().forEach(group -> group.setOpen(shouldExpand));
            }
        }
    }

    public <T> void setDatas(List<T> datas) {
        long startTime = System.currentTimeMillis();
        butCol.clear();
        loadComp = false;
        if (this.tableData != datas) {
            this.tableData.clear();
            if (datas != null) {
                this.tableData.addAll(datas);
            }
        }
        fcol = gridWnd.getFcol();
        rowNum = gridWnd.getRowNum();
        if (dataBinder == null) {
            dataBinder = new AnnotateDataBinder(gridWnd, true);
        }
        if (groupCol != null) {
            setModel(modelArray = new TableGroupModel(datas, new GridGroup(groupCol), gridWnd.multiple()));
        } else {
            if (modelList == null) {
                modelList = new TableModel();
                setModel(modelList);
            } else {
                modelList.clear();
            }
            if (datas != null) {
                modelList.addAll(datas);
            }
        }
        if (log.isDebugEnabled()) {
            long endTime = System.currentTimeMillis();
            log.debug("设置表格数据耗时: {} ms, 数据量: {}", (endTime - startTime),
                    datas != null ? datas.size() : 0);
        }
    }

    public Listitem getListitem(Object data) {
        if (data == null) return null;
        return getItems().stream()
                .filter(item -> ObjectUtil.equals(item.getValue(), data))
                .findFirst()
                .orElse(null);
    }

    public Component getComponent(Object data, String field) {
        if (data == null || field == null) return null;
        Listitem item = getListitem(data);
        if (item == null) return null;
        return dataBinder.getComponent(String.format("%s.%s", item.getUuid(), field));
    }

    public void render(Listitem item, Object data, int index) throws Exception {
        if (data == null) return;
        long startTime = 0;
        if (log.isDebugEnabled()) {
            startTime = System.currentTimeMillis();
        }
        item.setValue(data);
        if (item instanceof Listgroup group) {
            renderGroup(group, data);
        } else {
            renderNormalItem(item, data);
        }
        if (log.isDebugEnabled() && startTime > 0) {
            long endTime = System.currentTimeMillis();
            if (endTime - startTime > 10) { // 只记录耗时超过 10ms 的渲染
                log.debug("渲染行 {} 耗时: {} ms", index, (endTime - startTime));
            }
        }
    }

    private void renderGroup(Listgroup group, Object data) {
        StringBuilder labelBuilder = new StringBuilder();
        labelBuilder.append(ObjectUtil.defaultIfNull(BeanUtil.getFieldValue(data, groupCol), "")).append("(").append(group.getItemCount()).append(")");
        Listcell cell = new Listcell(labelBuilder.toString());
        if (firstSum > 0) {
            cell.setSpan(firstSum + 1);
        }
        group.appendChild(cell);
        group.setAttribute(groupCol, cell.getLabel());
        group.addForward("onClick", this, "onGroupSelect", group);
        Events.postEvent("onRenderGroup", gridWnd, group);
    }


    private void renderNormalItem(Listitem item, Object data) {
        dataBinder.bindBean(item.getUuid(), data);
        String indexValue;
        if (groupCol != null) {
            indexValue = String.valueOf(item.getListgroup().getItems().indexOf(item) + 1);
        } else {
            indexValue = String.valueOf(StrUtil.isBlank(fcol) ? rowNum++ : BeanUtil.getFieldValue(data, fcol));
        }
        Listcell indexCell = new Listcell(indexValue);
        List<Component> cells = new ArrayList<>(cfgList != null ? cfgList.size() + 1 : 1);
        cells.add(indexCell);
        if (cfgList != null) {
            for (AppConfig cfg : cfgList) {
                try {
                    cells.add(new TableCell(dataBinder, cfg, data, gridWnd, this, item));
                } catch (Exception e) {
                    log.error("创建表格单元格失败: {}", e.getMessage(), e);
                }
            }
        }
        item.getChildren().addAll(cells);
        if (hasEdit) {
            item.addForward("onClick", this, "onReload", item);
        }
        if (!isMultiple()) {
            item.addForward("onDoubleClick", gridWnd, "onOpenData", item);
        }
        Events.postEvent("onRender", gridWnd, item);
    }

    public void onAfterRender() {
        if (tableData.isEmpty()) {
            return;
        }
        long startTime = System.currentTimeMillis();
        if (gridWnd.isRenderAll()) {
            renderAll();
        }
        cfgList.stream().filter(e -> StrUtil.endWith(e.getHbType(), "Div")).forEach(e -> hide(e.getDispName(), !butCol.contains(e.getDispName())));
        processGroupTotals();
        dataBinder.loadAll();
        Events.postEvent("onTableAfterRender", gridWnd, this);
        if (log.isDebugEnabled()) {
            long endTime = System.currentTimeMillis();
            log.debug("表格渲染完成耗时: {} ms", (endTime - startTime));
        }
    }

    private void processGroupTotals() {
        List<Listgroup> groups = getGroups();
        if (groups == null || groups.isEmpty()) {
            return;
        }

        for (Listgroup group : groups) {
            List<Object> dataList = group.getItems().stream().map(Listitem::getValue).collect(Collectors.toList());
            if (dataList.isEmpty()) {
                continue;
            }
            List<Component> children = group.getChildren();
            if (children.size() > 1) {
                children.subList(1, children.size()).clear();
            }
            boolean hasSum = false;
            for (AppConfig cfg : cfgList) {
                String dispName = cfg.getDispName();
                if (sumCols.contains(dispName)) {
                    double sum = calculateSum(dataList, dispName);
                    String format = StrUtil.containsAnyIgnoreCase(cfg.getHbType(), "Integer", "Long") ? "%.0f" : "%.2f";
                    Listcell listcell = new Listcell(String.format(format, sum));
                    listcell.setStyle("text-align: center;color:red !important;");
                    group.appendChild(listcell);
                    group.setAttribute(dispName, sum);
                    hasSum = true;
                } else if (hasSum) {
                    group.appendChild(new Listcell());
                }
            }
        }
    }

    private double calculateSum(List<Object> dataList, String property) {
        double sum = 0;
        for (Object data : dataList) {
            Object value = BeanUtil.getFieldValue(data, property);
            if (value instanceof Number) {
                sum += ((Number) value).doubleValue();
            }
        }
        return sum;
    }

    @Override
    public void selectItem(Listitem item) {
        super.selectItem(item);
        Events.postEvent("onSelectItem", gridWnd, item);
    }

    public void onGroupSelect(Event event) {
        Listgroup group = (Listgroup) event.getData();
        if (group == null) return;
        boolean isSelected = group.isSelected();
        group.getItems().forEach(item -> {
            item.setSelected(isSelected);
            if (isSelected) {
                modelArray.addToSelection(item.getValue());
            } else {
                modelArray.removeFromSelection(item.getValue());
            }
        });
    }

    public void onReload(Event event) {
        Listitem item = (Listitem) event.getData();
        if (item == null) return;
        item.removeForward("onClick", this, "onReload");
        List<Listcell> cells = item.getChildren();
        cells.stream().filter(cell -> cell instanceof TableCell).map(cell -> (TableCell) cell).filter(cell -> cell.getCfg().isEdit()).forEach(cell -> enableCellEdit(cell, item));
        dataBinder.loadComponent(item);
        selectItem(item);
    }

    private void enableCellEdit(TableCell cell, Listitem item) {
        if (cell.getComp() instanceof CheckGroup || cell.getComp() instanceof Checkbox) {
            cell.setColedit(true);
            return;
        }
        AppConfig config = cell.getCfg();
        if (StrUtil.isBlank(config.getListName())) {
            InputElement inputComp = Utils.getComponent(config.getHbType());
            if (inputComp != null) {
                cell.replace(inputComp);
                inputComp.setStyle(String.format("text-align:%s !important;", config.getAlign()));
                inputComp.setInplace(true);
                inputComp.setHflex("1");
                inputComp.addEventListener("onClick", event -> selectItem(item));
            }
        }
    }

    @Override
    public void postChange(Object data, String field, Object value) {
        if (data == null || field == null) return;
        BeanUtil.setFieldValue(data, field, value);
        reLoadData(data);
    }

    public void onGroupLater(Event event) {
        if (event == null || event.getData() == null) return;
        groupCol = (String) event.getData();
        tableData = ListUtil.sort(tableData, new GridGroup(groupCol));
        setModel(modelArray = new TableGroupModel(tableData, new GridGroup(groupCol), gridWnd.multiple()));
    }

    @Override
    public void hideHeader(List<String> hideList) {
        if (hideList == null || headers == null) return;
        headers.forEach(header -> {
            String dispName = header.getCfg().getDispName();
            header.setVisible(dispName == null || !hideList.contains(dispName));
        });
    }

    public List<String> getVisibleHeader() {
        if (headers == null) return Collections.emptyList();
        return headers.stream().filter(AbstractComponent::isVisible).map(header -> header.getCfg().getDispName()).collect(Collectors.toList());
    }

    @Override
    public void hide(String dispName, boolean hide) {
        if (StrUtil.isBlank(dispName) || headers == null) return;
        headers.stream().filter(header -> StrUtil.equals(header.getCfg().getDispName(), dispName)).forEach(header -> header.setVisible(!hide));
    }

    @Override
    public void setSumValues(List<Double> valueList) {
        if (valueList == null || valueList.isEmpty() || listfoot == null) return;
        listfoot.getChildren().stream()
                .filter(footer -> footer.getAttribute("dispName") != null)
                .map(footer -> (Listfooter) footer)
                .forEach(footer -> {
                    String dispName = (String) footer.getAttribute("dispName");
                    int valueIndex = sumCols.indexOf(dispName);
                    if (valueIndex >= 0 && valueIndex < valueList.size()) {
                        footer.setLabel(String.format("%.2f", valueList.get(valueIndex)));
                    } else {
                        footer.setLabel("0.00");
                    }
                });
    }

    @Override
    public <T> List<T> getTableData() {
        return (List<T>) tableData;
    }

    @Override
    public List<String> getSumCols() {
        return sumCols;
    }

    public void addButtonColumn(String columnName) {
        if (StrUtil.isNotBlank(columnName)) {
            butCol.add(columnName);
        }
    }

    public boolean isEmpty() {
        return tableData == null || tableData.isEmpty();
    }

    public void refresh() {
        if (groupCol != null && modelArray != null) {
            modelArray.setGroupData(tableData.toArray());
        } else if (modelList != null) {
            modelList.clear();
            modelList.addAll(tableData);
        }
        onAfterRender();
    }

    public void setEditable(boolean editable) {
        hasEdit = editable;
    }

    public AppConfig getColumnConfig(int index) {
        if (cfgList == null || index < 0 || index >= cfgList.size()) {
            return null;
        }
        return cfgList.get(index);
    }

    public AppConfig findColumnConfig(String fieldName) {
        if (cfgList == null || StrUtil.isBlank(fieldName)) {
            return null;
        }
        return cfgList.stream().filter(config -> StrUtil.equals(config.getDispName(), fieldName)).findFirst().orElse(null);
    }

    public Header getHeader(int index) {
        if (headers == null || index < 0 || index >= headers.size()) {
            return null;
        }
        return headers.get(index);
    }

    public void selectData(Object data) {
        if (data == null) return;
        Listitem item = getListitem(data);
        if (item != null) {
            selectItem(item);
        }
    }
}

