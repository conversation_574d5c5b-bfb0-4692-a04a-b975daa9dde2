package com.sys.core.zul.api;

import com.sys.core.api.BaseDao;
import com.sys.core.cache.CacheServer;
import com.sys.entity.*;
import org.zkoss.zk.ui.Component;
import org.zkoss.zkplus.databind.TypeConverter;
import org.zkoss.zul.Listitem;

import java.util.List;

public interface GridConfig extends ToolConfig, TypeConverter {

    List<AppConfig> getConfigList();

    TableData getCurrTable();

    MainTable getMainTable();

    BaseDao getBaseDao();

    String getGroupCol();

    int getRowNum();

    String getFcol();

    String getFname();

    String getFnameWidth();

    String getVflex();

    int getRows();

    App getApp();

    int getFrozen();

    boolean multiple();

    boolean isCurExcel();

    boolean isRenderAll();

    Component getComponent(Listitem item, AppConfig cfg);

    List<Require> getRequires();

    CacheServer getCacheServer();

    List<ButtonCfg> getBtnCfg(String cfgName, String dispName);

}
