package com.sys.core.zul.api;

import com.alibaba.fastjson.JSONArray;
import com.sys.entity.Appconfig;
import com.sys.entity.Listvalue;
import org.zkoss.zk.ui.Component;
import org.zkoss.zul.impl.InputElement;

import java.util.List;
import java.util.Map;

public interface ToolConfig extends Component {
    boolean switchExcel();

    boolean item2top();

    JSONArray getAppItem();

    String getCfgName(String... type);

    List<Appconfig> getConfigSearch();

    void setDefaultSearch(InputElement input, String diapName);

    boolean addListValueCond(Map cond, Listvalue list);

    void setGroupCol(String groupCol);

    void setGroupSortType(String sortType);

    boolean isSaveDisabled();
}
