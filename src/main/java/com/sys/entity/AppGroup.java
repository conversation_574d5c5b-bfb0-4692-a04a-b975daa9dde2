package com.sys.entity;

import jakarta.persistence.*;
import lombok.Data;

@Data
@Entity
@Table(name = "S_APP_GROUP")
public class AppGroup implements java.io.Serializable, Refresh {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String grpNum;
    private String grpName;
    private String busCenter;
    private String userId;
    private String grpType;

    public boolean equals(Object obj) {
        return obj instanceof AppGroup && id != null ? id.equals(((AppGroup) obj).id) : super.equals(obj);
    }

    public int hashCode() {
        return id != null ? id.hashCode() : super.hashCode();
    }
}
