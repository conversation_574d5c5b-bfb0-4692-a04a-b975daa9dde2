package com.sys.entity;

import com.sys.core.api.Node;
import jakarta.persistence.*;
import lombok.Data;

@Data
@Entity
@Table(name = "s_department")
public class Department implements Node {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String deptname;
    private Long parentId;
    private Integer sortNo;
    private Boolean leaf=true;
    private String iconClass;

    public Boolean isLeaf() {
        return this.leaf;
    }

    @Override
    public String toString() {
        return deptname;
    }

    public boolean equals(Object obj) {
        return obj instanceof Department && id != null ? id.equals(((Department) obj).id) : super.equals(obj);
    }


    public int hashCode() {
        return id != null ? id.hashCode() : super.hashCode();
    }
}
