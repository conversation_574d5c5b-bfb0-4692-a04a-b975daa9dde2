package com.sys.entity;

import jakarta.persistence.*;
import lombok.Data;

@Data
@Entity
@Table(name = "s_require")
public class Require implements java.io.Serializable, Refresh {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String cfgName;
    private String dispName;
    private Long auths = 0L;
    private String grpNum;


    @Transient
    public Boolean isReadonly() {
        return 16 == (auths & 16);
    }

    @Transient
    public Boolean isRequire() {
        return 32 == (auths & 32);
    }

    @Transient
    public boolean getColedit() {
        return (auths & 4) == 4;
    }

    public void coledit(boolean coledit) {
        auths = coledit ? auths | 4 : auths ^ 4;
    }

    public void readonly(boolean readonly) {
        auths = readonly ? auths | 16 : auths ^ 16;
    }

    public void require(boolean require) {
        auths = require ? auths | 32 : auths ^ 32;
    }

    public boolean equals(Object obj) {
        return obj instanceof Require && id != null ? id.equals(((Require) obj).id) : super.equals(obj);
    }

    public int hashCode() {
        return id != null ? id.hashCode() : super.hashCode();
    }
}
