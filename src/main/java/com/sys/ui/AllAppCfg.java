package com.sys.ui;

import cn.hutool.core.util.StrUtil;
import com.sys.common.CacheUtil;
import com.sys.common.Constants;
import com.sys.common.Message;
import com.sys.common.Utils;
import com.sys.core.zul.GridTable;
import com.sys.core.zul.ListWnd;
import com.sys.core.zul.TopWnd;
import com.sys.entity.Appconfig;
import com.sys.entity.UserConfig;
import org.zkoss.zk.ui.event.Event;
import org.zkoss.zul.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public class AllAppCfg extends ListWnd {
    Menupopup popu;
    String cfgType;
    String cfgName;
    List<UserConfig> cfgList;
    TopWnd targetWnd;

    public AllAppCfg() {
        cfgName = getParam("cfg_name");
        targetWnd = getParam("targetWnd");
        if (getUserInfo().getGrpList().contains("1000")&& Constants.showCfg) {
            appendChild(popu = new Menupopup());
            Menuitem item = new Menuitem("显示配置");
            popu.appendChild(item);
            item.addForward("onClick", this, "onViewConfig");
        }
    }

    public void onViewConfig() {
        GridTable gridTable = (GridTable) getCurrTable();
        Object data = gridTable.getSelectedItem();
        Utils.openData("~./page/sys/view_info.zul", data, this);
    }

    public void setCfgType(String cfgType) {
        this.cfgType = cfgType;
        cfgName = cfgName + "#" + cfgType;
        cfgList = CacheUtil.getUserConfig(cfgName, getUser().getUserid());
    }


    @Override
    public Map addCondition(Map cond) {
        Utils.addOrderBy(cond, "asc", "sort");
        Utils.addCondition(cond, "eq", "cfgName", cfgName);
        return super.addCondition(cond);
    }

    public void onRender(Event event) {
        Listitem item = (Listitem) event.getData();
        item.setContext(popu);
    }

    public String getCfgName(String... type) {
        return String.format("<APP>%s#%s#%s%s", "APPCONFIG", getMainTable().getTabName(), getRelation().getRelatName(), type.length > 0 ? "#" + type[0] : "");
    }

    public void del(Event event) {
        Optional.of((Map) event.getData()).filter(map -> map.containsKey("target")).map(map -> (CompTarget) map.get("target")).ifPresent(item -> {
            if (Message.showQuestion("您确定要删除该配置") == Messagebox.YES) {
                getBaseDao().delete(item.getValue());
                item.setParent(null);
                parentWnd.setAttribute("change", true);
            }
        });
    }

    @Override
    public void onPaging() {
        if (getRelation().getRelatName().equals("app_cfg_view")) {
            getCurrTable().setDatas(CacheUtil.getAppconfig(cfgName, getUser().getUserid()));
        } else {
            super.onPaging();
        }
    }

    @Override
    public void change(Object data, String field, Object orgVal, Object value) {
        if (getRelation().getRelatName().equals("app_cfg_view")) {
            if (StrUtil.equals(field, "hide")) {
                Optional.of(data).map(e -> (Appconfig) data).ifPresent(cfg -> {
                    cfgList.stream().filter(e -> StrUtil.equals(e.getDispName(), cfg.getDispName())).findFirst().map(e -> {
                        e.setHide(cfg.isHide());
                        baseDao.update(e);
                        return e;
                    }).orElseGet(() -> {
                        UserConfig ucfg = new UserConfig();
                        ucfg.setCfgName(cfg.getCfgName());
                        ucfg.setDispName(cfg.getDispName());
                        ucfg.setUserid(getUser().getUserid());
                        ucfg.setHide(cfg.isHide());
                        baseDao.save(ucfg);
                        cfgList.add(ucfg);
                        return ucfg;
                    });
                    targetWnd.hide(cfg.getDispName(), cfg.isHide());
                });
            }
        } else {
            super.change(data, field, orgVal, value);
        }
        parentWnd.setAttribute("change", true);
    }

}
