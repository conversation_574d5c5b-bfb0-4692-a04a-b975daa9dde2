package com.sys.ui;

import cn.hutool.core.util.StrUtil;
import com.sys.core.zul.ChildWnd;
import com.sys.core.zul.ListWnd;
import org.zkoss.zul.CompTarget;
import com.sys.entity.Applist;
import org.zkoss.zk.ui.event.Events;
import org.zkoss.zul.Tab;

public class AppListWnd extends ChildWnd {
    @Override
    public void change(Object data, String field,Object orgVal, Object value) {
        super.change(data, field,orgVal, value);
        if (StrUtil.equals(field, "listType")) {
            setTabStatus();
        }
    }

    private void setTabStatus() {
        Applist applist = getMainData();
        Tab jt = (Tab) getFellow("jt");
        Tab dt = (Tab) getFellow("dt");
        jt.setVisible(StrUtil.equals(applist.getListType(), "静态值列表"));
        jt.setSelected(StrUtil.equals(applist.getListType(), "静态值列表"));
        dt.setVisible(StrUtil.equals(applist.getListType(), "动态值列表"));
        dt.setSelected(StrUtil.equals(applist.getListType(), "动态值列表"));
        Tab tab = jt.getTabbox().getSelectedTab();
        Events.postEvent("onSelect", tab, tab);
    }

    @Override
    public void binderTarget(CompTarget target) {
        super.binderTarget(target);
        Applist applist = getMainData();
        if (StrUtil.equals(applist.getListType(), "动态值列表")) {
            ListWnd listWnd = (ListWnd) getFellow("listtable");
            Events.postEvent("onSearch", listWnd, null);
        }
        setTabStatus();
    }
}
