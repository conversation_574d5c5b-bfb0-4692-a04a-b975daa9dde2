package com.sys.ui;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.useragent.UserAgent;
import cn.hutool.http.useragent.UserAgentUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.sys.common.CacheUtil;
import com.sys.common.Constants;
import com.sys.common.Utils;
import com.sys.core.server.LogServer;
import com.sys.core.server.UserServer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.zkoss.zk.ui.event.Event;
import org.zkoss.zul.Div;

import java.util.Optional;

@Slf4j
public class AuthLogin extends Div {
    @Autowired
    public UserServer server;
    @Autowired
    LogServer logServer;

    public AuthLogin() {
        Utils.autowireServer(this);
        Optional.ofNullable(Utils.getUserInfo()).ifPresent(userInfo -> {
            if (StrUtil.isNotBlank(userInfo.getIscUserId())) {
                String url = String.format("%s?access_token=%s", Constants.getProperty("config.OAUTH_USER"), userInfo.getToken());
                cn.hutool.json.JSONObject json = JSONUtil.parseObj(HttpUtil.get(url));
                if (json.containsKey("error")) {
                    Utils.addSession("userInfo", null);
                    Utils.sendRedirect(Constants.getProperty("config.OAUTH_FUNCTION"));
                } else {
                    Utils.sendRedirect("index");
                }
            } else {
                Utils.addSession("userInfo", null);
            }
        });
    }

    public void onLocalPage(Event event) {
        Optional.ofNullable(Utils.getUserInfo()).ifPresentOrElse(userInfo -> {
            String id = Utils.getHttpServletRequest().getSession().getId();
            if (ObjectUtil.equal(CacheUtil.getUserCache("session", id), userInfo)) {
                Utils.sendRedirect("index");
            } else {
                Utils.queuePush("userLog", logServer.userLog("越权访问", "系统", "auth", "警告", "失败", false));
                Utils.sendRedirect(Constants.getProperty("config.OAUTH_FUNCTION"));
            }
        }, () -> Optional.ofNullable((String) event.getData()).map(url -> StrUtil.subBetween(url, "access_token", "state")).map(token -> token.substring(1, token.length() - 1)).ifPresentOrElse(token -> {
            String url = String.format("%s?access_token=%s", Constants.getProperty("config.OAUTH_USER"), token);
            Utils.addSession("user_token", token);
            cn.hutool.json.JSONObject json = JSONUtil.parseObj(HttpUtil.get(url));
            if (!json.containsKey("error")) {
                String userid = json.getStr("iscUserSourceId");
                logServer.userLog("获取用户信息", "系统", token, "正常", "成功", null, JSON.toJSONString(json));
                Optional.ofNullable(server.getUserInfo(userid, true)).ifPresentOrElse(userInfo -> {
                    userInfo.setIscUserId(json.getStr("iscUserId"));
                    userInfo.setToken(token);
                    userInfo.setUserId(userid);
                    String userAgent = Utils.getHttpServletRequest().getHeader("USER-AGENT");
                    UserAgent agent = UserAgentUtil.parse(userAgent);
                    userInfo.setMobile(agent.isMobile());
                    userInfo.setUserAgent(userAgent);
                    userInfo.setUserIp(Utils.getIpAddress());
                    Utils.addSession("userInfo", userInfo);
                    userInfo.setMaxInterval(Utils.getHttpServletRequest().getSession().getMaxInactiveInterval());
                    Optional.of(Constants.userLog).filter(Boolean.TRUE::equals).ifPresent(e -> Utils.queuePush("userLog", logServer.userLog("用户登录", "系统", null, "警告", "成功", false)));
                    String id = Utils.getHttpServletRequest().getSession().getId();
                    userInfo.setSessionId(id);
                    CacheUtil.addUserCache("session", id, userInfo);
                    logServer.sysLog("登录认证", userInfo, "警告");
                    Utils.sendRedirect("index");
                }, () -> Utils.sendRedirect(Constants.getProperty("config.OAUTH_FUNCTION")));
            } else {
                Utils.sendRedirect(Constants.getProperty("config.OAUTH_FUNCTION"));
            }
        }, () -> Utils.sendRedirect(Constants.getProperty("config.OAUTH_FUNCTION"))));
    }
}
