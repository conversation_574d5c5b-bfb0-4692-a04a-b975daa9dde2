package com.sys.ui;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.useragent.UserAgent;
import cn.hutool.http.useragent.UserAgentUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.sys.common.CacheUtil;
import com.sys.common.Constants;
import com.sys.common.Utils;
import com.sys.core.server.LogServer;
import com.sys.core.server.UserServer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.zkoss.zk.ui.event.Event;
import org.zkoss.zul.Div;

import java.util.Optional;

@Slf4j
public class AuthLogin extends Div {
    @Autowired
    public UserServer server;
    @Autowired
    LogServer logServer;

    public AuthLogin() {

        Utils.autowireServer(this);
        Optional.ofNullable(Utils.getUserInfo()).ifPresent(userInfo -> {
            if (StrUtil.isNotBlank(userInfo.getIscUserId())) {
                String url = String.format("%s?access_token=%s", Constants.getProperty("config.OAUTH_USER"), userInfo.getToken());
                cn.hutool.json.JSONObject json = JSONUtil.parseObj(HttpUtil.get(url));
                if (json.containsKey("error")) {
                    Utils.addSession("userInfo", null);
                    Utils.sendRedirect(Constants.getProperty("config.OAUTH_FUNCTION"));
                } else {
                    Utils.sendRedirect("index");
                }
            } else {
                Utils.addSession("userInfo", null);
            }
        });
    }

    public void onLocalPage(Event event) {
        Optional.ofNullable(Utils.getUserInfo()).ifPresentOrElse(userInfo -> {
            Utils.sendRedirect("index");
        }, () -> Optional.ofNullable((String) event.getData()).map(url -> StrUtil.subBetween(url, "access_token", "state")).map(token -> token.substring(1, token.length() - 1)).ifPresentOrElse(token -> {
            String url = String.format("%s?access_token=%s", Constants.getProperty("config.OAUTH_USER"), token);
            Utils.addSession("user_token", token);
            cn.hutool.json.JSONObject json = JSONUtil.parseObj(HttpUtil.get(url));
            if (!json.containsKey("error")) {
                String userid = json.getStr("iscUserSourceId");
                logServer.userLog("获取用户信息", "系统", token, "正常", "成功", null, JSON.toJSONString(json));
                Optional.ofNullable(server.getUserInfo(userid, true)).ifPresentOrElse(userInfo -> {
                    userInfo.setIscUserId(json.getStr("iscUserId"));
                    userInfo.setToken(token);
                    userInfo.setUserId(userid);
                    String userAgent = Utils.getHttpServletRequest().getHeader("USER-AGENT");
                    UserAgent agent = UserAgentUtil.parse(userAgent);
                    userInfo.setMobile(agent.isMobile());
                    userInfo.setUserAgent(userAgent);
                    userInfo.setUserIp(Utils.getIpAddress());
                    userInfo.setMaxInterval(Utils.getHttpServletRequest().getSession().getMaxInactiveInterval());
                    String id = Utils.getHttpServletRequest().getSession().getId();
                    userInfo.setSessionId(id);
                    Utils.addSession("userInfo", userInfo);
                    Optional.of(Constants.userLog).filter(Boolean.TRUE::equals).ifPresent(e -> Utils.queuePush("userLog", logServer.userLog("用户登录", "系统", null, "警告", "成功", false)));
                    CacheUtil.addUserCache("session", id, userInfo);
                    logServer.sysLog("登录认证", userInfo, "警告");
                    Utils.sendRedirect("index");
                }, () -> Utils.sendRedirect(Constants.getProperty("config.OAUTH_FUNCTION")));
            } else {
                Utils.sendRedirect(Constants.getProperty("config.OAUTH_FUNCTION"));
            }
        }, () -> Utils.sendRedirect(Constants.getProperty("config.OAUTH_FUNCTION"))));
    }
}
