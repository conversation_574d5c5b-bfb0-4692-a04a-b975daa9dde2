package com.sys.ui;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.useragent.UserAgent;
import cn.hutool.http.useragent.UserAgentUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.sys.common.CacheUtil;
import com.sys.common.Constants;
import com.sys.common.Message;
import com.sys.common.Utils;
import com.sys.core.server.LogServer;
import com.sys.core.server.SpringContextHolder;
import com.sys.core.server.UserServer;
import org.springframework.beans.factory.annotation.Autowired;
import org.zkoss.zk.ui.event.Event;
import org.zkoss.zk.ui.event.Events;
import org.zkoss.zul.Checkbox;
import org.zkoss.zul.Div;
import org.zkoss.zul.Textbox;

import java.util.Map;
import java.util.Optional;

public class LoginDiv extends Div {
    @Autowired
    public UserServer server;
    private Checkbox uck, pck;
    private Textbox userid, pwd;
    private Map<String, String[]> param;

    public LoginDiv() {
        param = Utils.getHttpServletRequest().getParameterMap();
        Utils.autowireServer(this);
    }

    public void onCreate() {
        uck = (Checkbox) getFellow("uck");
        pck = (Checkbox) getFellow("pck");
        pwd = (Textbox) getFellow("pwd");
        userid = (Textbox) getFellow("loginname");
        if (param.containsKey("userid") && param.containsKey("pwd")) {
            JSONObject login = new JSONObject();
            login.put("userid", param.get("userid")[0]);
            login.put("pwd", param.get("pwd")[0]);
            Events.postEvent("onLogin", this, JSONUtil.toJsonStr(login));
        } else {
            String login = Utils.getCookie("login");
            if (StrUtil.isNotBlank(login)) {
                JSONObject json = JSONObject.parseObject(Base64.decodeStr(login));
                uck.setChecked(json.containsKey("uck") ? json.getBoolean("uck") : false);
                pck.setChecked(json.containsKey("pck") ? json.getBoolean("pck") : false);
                userid.setValue(json.getString("userid"));
                pwd.setValue(json.getString("pwd"));
            }
        }
    }

    public void onLogin(Event event) {
        JSONObject param = JSONObject.parseObject((String) event.getData());
        Optional.ofNullable(server.getUserInfo(param.getString("userid"), param.getString("pwd"))).ifPresentOrElse(userInfo -> {
            String userAgent = Utils.getHttpServletRequest().getHeader("USER-AGENT");
            UserAgent agent = UserAgentUtil.parse(userAgent);
            userInfo.setUserAgent(userAgent);
            userInfo.setBrowWidth(param.getIntValue("width"));
            userInfo.setBrowHeight(param.getIntValue("height"));
            userInfo.setUserIp(Utils.getIpAddress());
            userInfo.setMobile(agent.isMobile());
            Utils.addSession("userInfo", userInfo);
            param.put("uck", uck.isChecked());
            param.put("pck", pck.isChecked());
            Optional.of(uck.isChecked()).filter(Boolean.FALSE::equals).ifPresent(e -> param.remove("userid"));
            Optional.of(pck.isChecked()).filter(Boolean.FALSE::equals).ifPresent(e -> param.remove("pwd"));
            Utils.addCookie("login", Base64.encode(JSONUtil.toJsonStr(param)));
            String id = Utils.getHttpServletRequest().getSession().getId();
            userInfo.setSessionId(id);
            userInfo.setMaxInterval(Utils.getHttpServletRequest().getSession().getMaxInactiveInterval());
            if (CacheUtil.addUserCache("session", id, userInfo)) {
                Optional.of(Constants.userLog).filter(Boolean.TRUE::equals).ifPresent(e -> SpringContextHolder.getBean(LogServer.class).sysLog("登录认证", userInfo, "警告"));
                Utils.sendRedirect(userInfo.isMobile() ? "phone" : "index");
            } else {
                Message.showError("登录失败,请联系管理人员!");
            }
        }, () -> {
            getPage().setTitle("用户登录");
            Message.showError("用户名或密码错误");
        });
    }
}
