package com.sys.ui;

import cn.hutool.core.convert.Convert;
import com.sys.common.Utils;
import com.sys.core.zul.ListWnd;
import com.sys.entity.PageRelation;
import com.sys.entity.PageRelationDetial;
import com.sys.entity.TableCol;

import java.util.ArrayList;
import java.util.Map;
import java.util.Optional;

public class RelationDetial extends ListWnd {
    @Override
    public void add(Object data) {
        PageRelation relation = getMainData();
        Utils.openList("tabcol", getMainData(), true, this, result -> {
            Optional.ofNullable(Convert.toList(TableCol.class, result)).orElse(new ArrayList<>()).forEach(e -> {
                PageRelationDetial detial = new PageRelationDetial();
                detial.setTabName(e.getTabName());
                detial.setColName(e.getDispName());
                detial.setPageRelation(relation.getId());
                detial.setColDes(e.getColDes());
                detial.setHbType(e.getHbType());
                super.add(detial);
            });
        });
    }

    @Override
    public void addCondition(String listname, Map<String, Object> conds, Object main) {
        conds.clear();
        PageRelation relation = getMainData();
        String sql = " tab_name='" + relation.getSubTable() + "' and not exists(select 1 from s_page_relation_detial t where  t.page_relation=" + relation.getId() + " and t.col_name=this_.disp_name)";
        Utils.addCondition(conds, "sql", null, sql);
    }
}
