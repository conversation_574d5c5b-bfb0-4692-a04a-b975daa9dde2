package com.ydzj.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.GenericGenerator;

@Entity
@Data
@Table(name = "YDZJ_WORK_PLAN")
public class YdzjWorkPlan implements java.io.Serializable {
    @Id
    @GenericGenerator(name = "YDZJ_WORK_PLAN", strategy = "assigned")
    private Long id;
    private String deviceType;
    private java.util.Date deliveryTime;
    private String wtdw;
    private String inspectCode;
    private Long number;
    private String testItem;
    private String remark;
    private String busType;
    private Long idx;

    public boolean equals(Object obj) {
        return obj instanceof YdzjWorkPlan && id != null ? id.equals(((YdzjWorkPlan) obj).id) : super.equals(obj);
    }

    public int hashCode() {
        return id != null ? id.hashCode() : super.hashCode();
    }
}
