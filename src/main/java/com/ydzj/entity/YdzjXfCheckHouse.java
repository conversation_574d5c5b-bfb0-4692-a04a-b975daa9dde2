package com.ydzj.entity;

import com.sys.util.SnowflakeIdWorker;
import lombok.Data;

import java.util.*;

import jakarta.persistence.*;
import org.hibernate.annotations.GenericGenerator;

@Entity
@Data
@Table(name = "YDZJ_XF_CHECK_HOUSE")
public class YdzjXfCheckHouse implements java.io.Serializable {
    @Id
    @GenericGenerator(name = "YDZJ_WORK_PLAN", strategy = "assigned")
    private Long id = SnowflakeIdWorker.generateId(1, 1);
    private Long orderId;
    private String house;
    private Integer houseNum = 1;
    private Long areaId;
    private java.util.Date createTime;
    private java.util.Date updateTime;

    public boolean equals(Object obj) {
        return obj instanceof YdzjXfCheckHouse && id != null ? id.equals(((YdzjXfCheckHouse) obj).id) : super.equals(obj);
    }

    public String toString() {
        return house;
    }

    public int hashCode() {
        return id != null ? id.hashCode() : super.hashCode();
    }
}
