package com.ydzj.ui.base;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sys.common.*;
import com.sys.core.api.BaseDao;
import com.sys.core.zul.RecWnd;
import com.sys.entity.Document;
import com.ydzj.entity.*;
import com.ydzj.server.DataServer;
import io.keikai.api.*;
import io.keikai.api.model.Picture;
import io.keikai.api.model.Sheet;
import io.keikai.model.*;
import io.keikai.ui.AuxAction;
import io.keikai.ui.CellSelectionType;
import io.keikai.ui.Spreadsheet;
import io.keikai.ui.event.*;
import io.keikai.ui.impl.undo.AutoFillCellAction;
import io.keikai.ui.impl.undo.InsertCellAction;
import io.keikai.ui.sys.UndoableAction;
import io.keikai.ui.sys.UndoableActionManager;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.zkoss.image.AImage;
import org.zkoss.io.Files;
import org.zkoss.util.media.Media;
import org.zkoss.util.resource.Labels;
import org.zkoss.zk.au.AuRequest;
import org.zkoss.zk.ui.event.Event;
import org.zkoss.zk.ui.event.Events;
import org.zkoss.zul.*;

import java.io.File;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Getter
public class OrderCheckParam extends Spreadsheet {
    private static final Logger log = LoggerFactory.getLogger(OrderCheckParam.class);
    String src = "web/page/sys/blank.xlsx";
    Map param;
    RecWnd recWnd;
    JSONObject checkJson;//sheet:[data,list]
    DeviceData device;
    List<YdzjCheckParam> paramList;
    @Autowired
    BaseDao baseDao;
    @Autowired
    DataServer server;
    JSONObject groupInfo = new JSONObject();
    Map<String, Map<String, YdzjEquipment>> equipmentMap = new HashMap<>();
    Map<String, YdzjCheckParam> checkParamMap;
    Map<String, List<SCell>> chooseCell = new HashMap<>();

    public OrderCheckParam(DeviceData device, Map param) {
        this.param = param;
        setId(StrUtil.uuid());
        setBaseParam();
        Utils.autowireServer(this);
        initSheet(device);
    }

    public OrderCheckParam() {
        param = Utils.getCurrent().getArg();
        setBaseParam();
        Utils.autowireServer(this);
        CompTarget target = (CompTarget) param.get("target");
        initSheet(target.getValue());
    }

    private void setBaseParam() {
        setVflex("1");
        setHflex("1");
        recWnd = (RecWnd) param.get("parentWnd");
        setShowToolbar(false);
        setShowSheetbar(false);
        setShowFormulabar(false);
        setShowContextMenu(false);
        setHiderowhead(true);
        setHidecolumnhead(true);
        removeToolbarButton(AuxAction.NEW_BOOK);
        removeToolbarButton(AuxAction.SAVE_BOOK);
        removeToolbarButton(AuxAction.CLOSE_BOOK);
        removeToolbarButton(AuxAction.EXPORT_PDF);
        removeToolbarButton(AuxAction.PIE_CHART);
        removeToolbarButton(AuxAction.PIE_CHART_3D);
        removeToolbarButton(AuxAction.INSERT_PICTURE);
        removeToolbarButton(AuxAction.HYPERLINK);
        removeToolbarButton(AuxAction.INSERT_CHART);
        removeToolbarButton(AuxAction.HORIZONTAL_ALIGN);
        //removeToolbarButton(AuxAction.GRIDLINES);
        removeToolbarButton(AuxAction.SORT_AND_FILTER);
        removeToolbarButton(AuxAction.DELETE);
        removeToolbarButton(AuxAction.ADD_SHEET);
        removeToolbarButton(AuxAction.DELETE_SHEET);
        removeToolbarButton(AuxAction.COPY_SHEET);
        removeToolbarButton(AuxAction.RENAME_SHEET);
        removeToolbarButton(AuxAction.PROTECT_SHEET);
        disableUserAction(AuxAction.ADD_SHEET, true);
    }


    public Map<String, List<Object>> getCellSelect(SSheet sheet) {
        Map<String, List<Object>> cellSelect = new HashMap<>();
        paramList.stream().filter(e -> StrUtil.isNotBlank(e.getListName())).forEach(e -> {
            List<String> checkRange = Arrays.asList(e.getListName().split(","));
            String hql = "from YdzjEquipment t where t.equRange in (:checkRange)";
            Map<String, Object> param = new HashMap<>();
            param.put("checkRange", checkRange);
            List<YdzjEquipment> equList = baseDao.findHql(hql, param);
            Map<String, YdzjEquipment> equMap = equList.stream().collect(Collectors.toMap(q -> StrUtil.concat(true, q.getEquipmentName(), " ", q.getEquipmentCode()), q -> q));
            cellSelect.put(e.getReference(), equMap.keySet().stream().collect(Collectors.toUnmodifiableList()));
            equipmentMap.put(e.getReference(), equMap);
        });
        return cellSelect;
    }

    public void initSheet(Object data) {
        if (data instanceof DeviceData temp) {
            Optional.of(ObjectUtil.equal(device, data)).filter(Boolean.FALSE::equals).ifPresent(t -> {
                Optional.ofNullable(temp.getParamFile()).ifPresentOrElse(param -> Optional.ofNullable((Document) baseDao.find(Document.class, temp.getParamFile())).filter(document -> document.getFilepath() != null).map(Document::getFilepath).ifPresentOrElse(filepath -> {
                    device = temp;
                    setSrc(filepath);
                    chooseCell.clear();
                    initParamCell(device);
                }, () -> setSrc(src)), () -> setSrc(src));
                setDisplayGridlines(false);
            });
            invalidate();
        }
    }

    public boolean save() {
        return baseDao.update(device);
    }


    public void initParamCell(DeviceData device) {
        SCellStyleBuilder builder = getSBook().createCellStyle();
        builder.borderBottom(SBorder.BorderType.THIN);
        builder.borderLeft(SBorder.BorderType.THIN);// 左边框
        builder.borderTop(SBorder.BorderType.THIN);// 上边框
        builder.borderRight(SBorder.BorderType.THIN);// 右边框
        builder.alignment(SCellStyle.Alignment.LEFT);
        builder.verticalAlignment(SCellStyle.VerticalAlignment.CENTER);
        builder.alignment(SCellStyle.Alignment.CENTER);
        builder.backColor("#F9B350");
        builder.fillPattern(SFill.FillPattern.SOLID);
        SCellStyle style = builder.build();

        paramList = server.getCheckParam(device.getCheckId());
        checkJson = device.getCheckResult();
        if (checkJson.isEmpty()) {
            YdzjOrder order = baseDao.find(YdzjOrder.class, device.getOrderId());
            JSONObject data = new JSONObject();
            BeanUtil.copyProperties(order.getData(), data);
            JSONObject json = (JSONObject) JSONObject.toJSON(order);
            json.remove("data");
            data.putAll(json);
            JSONObject djson = (JSONObject) JSONObject.toJSON(device);
            djson.remove("data");
            data.putAll(djson);
            checkJson.put("data", data);
        }
        JSONObject dbData = checkJson.getJSONObject("data");
        setDefaultValue(dbData);
        SSheet sheet = getSelectedSSheet();
        List<String> arryKeys = new ArrayList<>();
        YdzjCheckDevice checkDevice = server.getCheckDevice(device.getCheckId());
        int keyInx = Optional.ofNullable(checkDevice.getArrayRow()).map(row -> {
            groupInfo.put("key", arryKeys);
            groupInfo.put("row", row + 1);
            return row - 2;
        }).orElse(0);
        final int[] maxCol = {0};
        checkParamMap = paramList.stream().collect(Collectors.toMap(YdzjCheckParam::getVariable, e -> e, (k1, k2) -> k1));
        sheet.getRowIterator().forEachRemaining(row -> row.getCellIterator().forEachRemaining(cel -> {
            int rowIndex = row.getIndex();
            if (maxCol[0] < cel.getColumnIndex()) {
                maxCol[0] = cel.getColumnIndex();
            }
            if (keyInx > 0 && (keyInx == rowIndex || keyInx == rowIndex - 1)) {
                Optional.ofNullable(cel.getValue()).map(StrUtil::toString).filter(val -> StrUtil.containsAll(val, "«", "»")).map(val -> StrUtil.subBetween(val, "«", "»")).ifPresent(val -> {
                    if (!arryKeys.contains(val)) {
                        arryKeys.add(val);
                    }
                    Optional.ofNullable(checkParamMap.get(val)).map(YdzjCheckParam::getChooseVariable).filter(StrUtil::isNotBlank).ifPresent(choose -> {
                        cel.setChooseVariable(choose);
                        if (!arryKeys.contains(choose)) {
                            arryKeys.add(choose);
                        }
                    });
                    cel.setVariable(val);
                });
            } else {
                Optional.ofNullable(cel.getValue()).map(StrUtil::toString).filter(val -> StrUtil.containsAll(val, "«", "»")).map(val -> StrUtil.subBetween(val, "«", "»")).ifPresent(val -> {
                    cel.setVariable(val);
                    cel.setData(dbData);
                    if (checkParamMap.containsKey(val)) {
                        YdzjCheckParam cparam = checkParamMap.get(val);
                        cel.setChooseVariable(cparam.getChooseVariable());
                        if (cparam.getImageFile()) {
                            loadImage(getSelectedSheet(), cparam.getReference(), dbData.getString(cparam.getVariable()));
                        } else if (cparam.getFormula() != null) {
                            cel.setValue(cparam.getFormula());
                        } else if (dbData.containsKey(val)) {
                            cel.setValue(dbData.get(val));
                        } else if (StrUtil.isNotBlank(cparam.getDefaultValue())) {
                            cel.setValue(cparam.getDefaultValue());
                            dbData.put(val, cparam.getDefaultValue());
                        } else {
                            cel.setValue(null);
                        }
                    } else {
                        cel.setValue(dbData.getOrDefault(val, null));
                    }
                    Optional.ofNullable(cel.getChooseVariable()).ifPresent(choose -> {
                        if (chooseCell.containsKey(choose)) {
                            chooseCell.get(choose).add(cel);
                        } else {
                            chooseCell.put(choose, new ArrayList<>());
                            chooseCell.get(choose).add(cel);
                        }
                    });
                });
            }
        }));

        if (!groupInfo.isEmpty()) {
            JSONArray keys = groupInfo.getJSONArray("key");
            Integer row = groupInfo.getInteger("row");
            if (!dbData.containsKey("list")) {
                JSONArray list = new JSONArray();
                dbData.put("list", list);
                setDefaultArray(list, keys, device.getCheckNum());
            }
            JSONArray list = dbData.getJSONArray("list");
            int insetRow = list.size() > device.getCheckNum() ? list.size() : device.getCheckNum();
            if (insetRow > 1) {
                insertRow(row, (insetRow - 1) * 2);
                dbData.putIfAbsent("list", new JSONArray());
                keys.forEach(dbData::remove);
            } else {
                JSONObject json = list.getJSONObject(0);
                sheet.getRowIterator(row - 3, row - 2).forEachRemaining(rw -> rw.getCellIterator().forEachRemaining(cel -> {
                    if (keys.contains(cel.getVariable())) {
                        if (ObjectUtil.isNotEmpty(json.get(cel.getVariable()))) {
                            cel.setValue(json.get(cel.getVariable()));
                        } else {
                            Optional.ofNullable(checkParamMap.get(cel.getVariable())).filter(param -> param.getDefaultValue() != null).ifPresentOrElse(param -> {
                                cel.setValue(param.getDefaultValue());
                                json.put(cel.getVariable(), param.getDefaultValue());
                            }, () -> {
                                cel.setValue(null);
                                json.put(cel.getVariable(), "");
                            });
                        }
                        cel.setData(json);
                    }
                }));
                invalidate();
            }
        }
        setMaxVisibleColumns(maxCol[0] + 2);
        // setMaxVisibleRows(sheet.getEndRowIndex() + (groupInfo.containsKey("row") ? groupInfo.getInteger("row") : 2));
    }

    public void setDefaultValue(JSONObject data) {
        YdzjOrder order = baseDao.find(YdzjOrder.class, device.getOrderId());
        if (StrUtil.equals(order.getOrderType(), "施工机具")) {
            YdzjOrderDeviceItem target = (YdzjOrderDeviceItem) device;
            data.put("syNum", target.getCheckNum());
            data.put("reportNo", target.getReportNo());
            data.put("sampleName", target.getCheckName());
            data.put("items", target.getCheckItem());
            data.put("company", order.getData().getString("check_addr"));
            data.put("testCategory", target.getCheckItem());
            data.put("testDate", DateUtil.format(new Date(), "yyyy.MM.dd"));
            JSONObject base = CacheUtil.getCacheByType("param");
            if (null != base) {
                data.putAll(base);
            }
        }
    }

    public void setDefaultArray(JSONArray array, JSONArray keys, int size) {
        YdzjOrder mainData = recWnd.getMainData();
        for (int i = 1; i <= size; i++) {
            JSONObject obj = new JSONObject();
            obj.put("no", i);
            obj.put("sampleNo", String.format("%s-%03d", mainData.getWtdId(), i));
            array.add(obj);
            keys.forEach(v -> {
                obj.putIfAbsent((String) v, "");
            });
        }
    }

    public void initBaseParam(JSONObject baseParam) {
        SSheet sheet = getSelectedSSheet();
        baseParam.forEach((k, v) -> {
            YdzjCheckParam param = paramList.stream().filter(e -> StrUtil.equals(e.getVariable(), k)).findFirst().orElse(null);
            if (param != null) {
                SCell cell = sheet.getCell(param.getReference());
                cell.setValue(v);
                JSONObject json = cell.getData();
                json.put(k, v);
            }
        });
        baseDao.update(device);
        invalidate();
    }

    private AreaRefWithType getSelectionIfAny(Map data) {
        Object rangesData;
        if (data.containsKey("selection")) {
            rangesData = (List) data.get("selection");
        } else {
            rangesData = new ArrayList();
            ((List) rangesData).add(data);
        }

        Set<AreaRefWithType> selection = new LinkedHashSet();
        Iterator var4 = ((List) rangesData).iterator();

        while (var4.hasNext()) {
            JSONObject rangeData = (JSONObject) var4.next();
            if (rangeData.containsKey("tRow") && rangeData.containsKey("tRow") && rangeData.containsKey("tRow") && rangeData.containsKey("tRow")) {
                int tRow = (Integer) rangeData.get("tRow");
                int bRow = (Integer) rangeData.get("bRow");
                int lCol = (Integer) rangeData.get("lCol");
                int rCol = (Integer) rangeData.get("rCol");
                selection.add(new AreaRefWithType(tRow, lCol, bRow, rCol, CellSelectionType.CELL));
            }
        }
        return selection.stream().findFirst().orElse(null);
    }

    @Override
    public void stopEditing(StopEditingEvent event) {
        SSheet sheet = event.getSheet().getInternalSheet();
        SCell scell = sheet.getCell(event.getRow(), event.getColumn());
        if (scell.getData() == null || scell.getType() == SCell.CellType.FORMULA) {
            event.cancel();
        }
    }

    public void onAfterCellChange(CellAreaEvent event) {
        SSheet sheet = event.getSheet().getInternalSheet();
        for (int i = event.getRow(); i <= event.getLastRow(); i++) {
            for (int j = event.getColumn(); j <= event.getLastColumn(); j++) {
                cellEditTex(sheet, i, j);
            }
        }
        Optional.ofNullable(recWnd).map(e -> (Treeitem) e.getAttribute("target")).ifPresentOrElse(item -> {
            item.setLabel(item.getValue() + "[提交中]");
            DeviceData treeData = item.getValue();
            if (!treeData.getDataStatus()) {
                treeData.setDataStatus(true);
                baseDao.update(treeData);
            }
        }, () -> {
            if (hasFellow("param_tree")) {
                Tree tree = (Tree) getFellow("param_tree");
                Optional.ofNullable(tree.getSelectedItem()).ifPresent(e -> {
                    YdzjOrderDevice orderDevice = e.getValue();
                    if (!orderDevice.getDataStatus()) {
                        orderDevice.setDataStatus(true);
                        baseDao.update(orderDevice);
                        e.setLabel(e.getValue().toString() + "[提交中]");
                    }
                });
            }
        });
    }

    public boolean canDelCellData(Sheet sheet, AreaRef ref) {
        SSheet ssheet = sheet.getInternalSheet();
        for (int i = ref.getRow(); i <= ref.getLastRow(); i++) {
            for (int j = ref.getColumn(); j <= ref.getLastRow(); j++) {
                if (ssheet.getCell(i, j).getData() == null) {
                    return false;
                }
            }
        }
        return true;
    }

    public void service(AuRequest request, boolean everError) {
        String cmd = request.getCommand();
        if (StrUtil.equals(cmd, "onCellSelectionUpdate")) {
            return;
        }
        super.service(request, everError);
    }

    public void onAfterUndoableManagerAction(UndoableActionManagerEvent event) {
        UndoableAction action = (UndoableAction) event.getData();
        if (null != action) {
            if (action instanceof InsertCellAction) {//插入行后自动引用上一行格式
                Sheet sheet = action.getRedoSheet();
                int row = action.getRedoSelection().getRow();
                JSONArray list = checkJson.getJSONObject("data").getJSONArray("list");
                AreaRef dest = new AreaRef(row - 2, 1, row + (list.size() - 2) * 2, sheet.getLastColumn(row));
                AreaRef src = new AreaRef(row - 2, dest.getColumn(), row - 1, dest.getLastColumn());
                UndoableActionManager uam = getUndoableActionManager();
                uam.doAction(new AutoFillCellAction(Labels.getLabel("zss.undo.fillCell"), sheet, src.getRow(), src.getColumn(), src.getLastRow(), src.getLastColumn(), sheet, dest.getRow(), dest.getColumn(), dest.getLastRow(), dest.getLastColumn(), Range.AutoFillType.DEFAULT));
                setMaxVisibleRows(sheet.getLastRow() + 2);
            } else if (action instanceof AutoFillCellAction) {
                SSheet sheet = action.getRedoSheet().getInternalSheet();
                AutoFillCellAction fill = (AutoFillCellAction) action;
                AreaRef fillRef = fill.getRedoSelection();
                JSONArray list = checkJson.getJSONObject("data").getJSONArray("list");
                sheet.getRowIterator(fillRef.getRow() + 2, fillRef.getLastRow() + 1).forEachRemaining(row -> row.getCellIterator().forEachRemaining(cel -> {
                    SCell pcel = sheet.getCell(cel.getRowIndex() - 2, cel.getColumnIndex(), true);
                    cel.setVariable(pcel.getVariable());
                    cel.setChooseVariable(pcel.getChooseVariable());
                }));
                AtomicInteger dataInx = new AtomicInteger();
                int startRow = fillRef.getRow();
                AtomicReference<JSONObject> data = new AtomicReference<>();
                List<Integer> loadRows = new ArrayList<>();
                sheet.getRowIterator(fillRef.getRow(), fillRef.getLastRow() + 1).forEachRemaining(row -> row.getCellIterator().forEachRemaining(cel -> {
                    if (StrUtil.isNotBlank(cel.getVariable())) {
                        if ((cel.getRowIndex() - startRow) % 2 == 0 && !loadRows.contains(cel.getRowIndex())) {
                            data.set(list.getJSONObject(dataInx.get()));
                            dataInx.getAndIncrement();
                            loadRows.add(cel.getRowIndex());
                            loadRows.add(cel.getRowIndex() + 1);
                        }
                        cel.setData(data.get());
                        Optional.ofNullable(checkParamMap.get(cel.getVariable())).ifPresent(check -> {
                            if (data.get().containsKey(check.getVariable()) && StrUtil.isNotBlank(data.get().getString(check.getVariable()))) {
                                cel.setValue(data.get().get(check.getVariable()));
                            } else if (StrUtil.isNotBlank(check.getDefaultValue())) {
                                data.get().put(check.getVariable(), check.getDefaultValue());
                                cel.setValue(check.getDefaultValue());
                            } else {
                                cel.setValue("");
                            }
                        });
                        Optional.ofNullable(cel.getChooseVariable()).ifPresent(choose -> {
                            if (chooseCell.containsKey(choose)) {
                                chooseCell.get(choose).add(cel);
                            } else {
                                chooseCell.put(choose, new ArrayList<>());
                                chooseCell.get(choose).add(cel);
                            }
                        });
                    }
                }));
                invalidate();
            }
        }
    }

    @Override
    public void cellEditTex(SSheet sheet, int row, int col) {
        SCell cell = sheet.getCell(row, col);
        String variable = cell.getVariable();
        if (StrUtil.isNotBlank(variable)) {
            JSONObject json = cell.getData();
            Object value = cell.getValue();
            if (value instanceof Double) {
                value = String.format("%.2f", value);
            } else if (value instanceof Long && (((Number) value).intValue() == (Long) value)) {
                value = ((Number) value).intValue();
            }

            if (!ObjectUtil.equal(value, json.get(variable))) {
                json.put(variable, value);
                String refer = cell.getReferenceString();
                if (equipmentMap.containsKey(refer)) {
                    String celStr = cell.getReferenceString().replaceAll("[a-zA-Z]", "");
                    YdzjCheckParam param = paramList.stream().filter(e -> StrUtil.containsAny(e.getParamName(), "有效期") && StrUtil.equals(e.getReference().replaceAll("[a-zA-Z]", ""), celStr)).findFirst().orElse(null);
                    if (param != null) {
                        String validityDate = DateUtil.format(equipmentMap.get(refer).get(value).getNextVerify(), "yyyy.MM.dd");
                        json.put(param.getVariable(), validityDate);
                        SCell cell1 = sheet.getCell(param.getReference());
                        CellEvent event = new StopEditingEvent("onStopEditing", this, getSelectedSheet(), cell1.getRowIndex(), cell1.getColumnIndex(), validityDate);
                        Events.postEvent(event);
                        Events.postEvent(new Event("onStopEditingImpl", this, new Object[]{"", event, "inlineEditing"}));
                    }
                }
                device.setDataStatus(true);
                if (device.getId() == null) {
                    baseDao.save(device);
                } else {
                    device.setDataStatus(true);
                    baseDao.update(device);

                }
            }
        }
    }

    public boolean checkEditParam() {
        JSONObject data = checkJson.getJSONObject("data");
        JSONArray error = new JSONArray();
        paramList.stream().filter(p -> StrUtil.isBlank(p.getGroupName())).forEach(p -> {
            if (p.getRequired() && !data.containsKey(p.getVariable())) {
                error.add(p.getParamName());
            }
        });
        if (!error.isEmpty()) {
            Message.showError("请完成检测参数填写:\r\n" + error.stream().map(e -> (String) e).collect(Collectors.joining("\r\n")));
        }
        return true;
    }

    public JSONArray uploads(String path) throws Exception {
        JSONArray arrays = new JSONArray();
        Window window = Utils.createWindow("~./page/sys/up_img.zul", null);
        window.doModal();
        Optional.ofNullable((Media) window.getAttribute("data")).ifPresent(media -> {
            try {
                JSONObject rstMap = new JSONObject();
                String savename = StrUtil.uuid();
                String filePath = Constants.resourceLocation + path + File.separator + savename;
                Files.copy(new File(filePath), media.getStreamData());
                rstMap.put("url", Constants.webUrl + "/files/" + path + "/" + savename);
                rstMap.put("upload", true);
                rstMap.put("savename", savename);
                rstMap.put("filepath", filePath);
                rstMap.put("filename", media.getName());
                rstMap.put("byte_data", media.getByteData());
                rstMap.put("width", ((AImage) media).getWidth());
                rstMap.put("height", ((AImage) media).getHeight());
                arrays.add(rstMap);
            } catch (Exception e) {
                log.error("上传图片失败", e);
            }
        });

        return arrays;
    }

    public void uploadImage(Sheet sheet, int row, int col, boolean first) {
        try {
            JSONArray arrays = uploads("/upload/image/");
            if (!arrays.isEmpty()) {
                List<Document> uploads = arrays.toJavaList(Document.class);
                SSheet ssheet = sheet.getInternalSheet();
                CellRegion region = ssheet.getMergedRegion(row, first ? 1 : col);
                Range range;
                int width = 0, height = 0;
                if (null != region) {
                    for (int i = region.getRow(); i <= region.getLastRow(); i++) {
                        height += ssheet.getRow(i).getHeight();
                    }
                    for (int i = region.getColumn(); i <= region.getLastColumn(); i++) {
                        width += ssheet.getColumn(i).getWidth();
                    }
                    range = Ranges.range(sheet, region.getRow(), region.getColumn(), region.getLastRow(), region.getLastColumn());
                } else {
                    width = IntStream.rangeClosed(1, col).map(i -> ssheet.getColumn(i).getWidth()).sum() / uploads.size();
                    height = ssheet.getRow(row).getHeight();
                    range = Ranges.range(sheet, row, first ? 1 : col, row, first ? 1 : col);
                }
                baseDao.save(uploads);
                int finalWidth = (int) ((double) width * 0.8);
                int finalHeight = (int) (height * 0.8);
                uploads.forEach(e -> SheetOperationUtil.addPicture(range, e.getByteData(), Picture.Format.JPEG, finalWidth > e.getWidth() ? e.getWidth() : finalWidth, finalHeight > e.getHeight() ? e.getHeight() : finalHeight));
                String variable = ssheet.getCell(row, first ? 1 : col).getVariable();
                JSONObject json = ssheet.getCell(row, first ? 1 : col).getData();
                json.put(variable, uploads.stream().map(Document::getSavename).collect(Collectors.joining(",")));
                baseDao.saveOrUpdate(device);
            } else {
                Message.showError("请上传有效的图片");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private void loadImage(Sheet base, String ref, String files) {
        try {
            if (StrUtil.isNotBlank(files)) {
                List<String> fileList = Arrays.stream(files.split(",")).toList();
                int width = 0;
                int height = 0;
                Range range;
                SSheet sheet = base.getInternalSheet();
                SCell cell = sheet.getCell(ref);
                CellRegion region = sheet.getMergedRegion(ref);
                if (region != null) {
                    for (int i = region.getRow(); i <= region.getLastRow(); i++) {
                        height += sheet.getRow(i).getHeight();
                    }
                    for (int i = region.getColumn(); i <= region.getLastColumn(); i++) {
                        width += sheet.getColumn(i).getWidth();
                    }
                    range = Ranges.range(base, region.getRow(), region.getColumn(), region.getLastRow(), region.getLastColumn());
                } else {
                    height = sheet.getRow(cell.getRowIndex()).getHeight();
                    width = sheet.getColumn(cell.getColumnIndex()).getWidth();
                    range = Ranges.range(base, cell.getRowIndex(), cell.getColumnIndex(), cell.getRowIndex(), cell.getColumnIndex());
                }
                int imgWidth = (int) (((double) width / fileList.size()) * 0.8);
                int imgHeight = (int) (height * 0.8);
                fileList.forEach(e -> {
                    Document doc = baseDao.find(Document.class, e);
                    if (null != doc) {
                        SheetOperationUtil.addPicture(range, doc.getByteData(), Picture.Format.JPEG, imgWidth > doc.getWidth() ? doc.getWidth() : imgWidth, imgHeight > doc.getHeight() ? doc.getHeight() : imgHeight);
                    }
                });
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public void paste(SSheet sheet, List<List<Object>> values, AreaRef area) {
        SCell cell;
        for (int i = area.getRow(); i <= area.getLastRow(); i++) {
            for (int j = area.getColumn(); j <= area.getLastColumn(); j++) {
                cell = sheet.getCell(i, j);
                String variable = cell.getVariable();
                if (variable != null) {
                    JSONObject json = cell.getData();
                    json.put(variable, cell.getValue());
                }
            }
        }
        Optional.ofNullable(device.getId()).ifPresentOrElse(p -> baseDao.save(device), () -> baseDao.update(device));
        Optional.ofNullable(recWnd).map(e -> (Treeitem) e.getAttribute("target")).ifPresentOrElse(item -> {
            DeviceData treeData = item.getValue();
            if (!treeData.getDataStatus()) {
                treeData.setDataStatus(true);
                baseDao.update(treeData);
            }
            item.setLabel(device.toString() + "[提交中]");
        }, () -> {
            if (hasFellow("param_tree")) {
                Tree tree = (Tree) getFellow("param_tree");
                Optional.ofNullable(tree.getSelectedItem()).ifPresent(e -> {
                    YdzjOrderDevice orderDevice = e.getValue();
                    if (!orderDevice.getDataStatus()) {
                        orderDevice.setDataStatus(true);
                        baseDao.update(orderDevice);
                    }
                    e.setLabel(e.getValue().toString() + "[提交中]");
                });
            }
        });
    }


    private JSONObject getGroupInfo(DeviceData data) {
        SSheet sheet = getSelectedSSheet();
        JSONObject json = new JSONObject();
        YdzjCheckDevice check = server.getCheckDevice(data.getCheckId());
        if (null != check && check.getArrayRow() != null) {
            int row = check.getArrayRow();
            Map<Integer, String> group = new HashMap<>();
            sheet.getRow(row - 1).getCellIterator().forEachRemaining(cell -> {
                if (StrUtil.isNotBlank(cell.getVariable())) {
                    group.put(cell.getColumnIndex(), cell.getVariable());
                }
            });
            json.put("key", group);
            json.put("row", row + 1);
        }
        return json;
    }

    public void insertRow(int startRow, int insetRow) {
        if (insetRow > 0) {
            Sheet sheet = getSelectedSheet();
            JSONObject json = new JSONObject();
            json.put("sheetId", getId());
            json.put("tag", "toolbar");
            json.put("lCol", 1);
            json.put("rCol", 1);
            json.put("type", "row");
            json.put("action", "insertSheetRow");
            json.put("tRow", startRow - 1);
            json.put("bRow", startRow + insetRow - 2);
            AuxActionEvent evt = new AuxActionEvent("onAuxAction", this, sheet, "insertSheetRow", getSelectionIfAny(json), json);
            Events.postEvent(evt);
        }
    }

    public void cellChange(SCell cell) {
        Object cval = cell.getValue();
        JSONObject json = cell.getData();
        json.put(cell.getVariable(), cval);
        CellEvent cevent = new StopEditingEvent("onStopEditing", this, getSelectedSheet(), cell.getRowIndex(), cell.getColumnIndex(), Convert.toStr(cval, ""));
        Events.postEvent(cevent);
        Events.postEvent(new Event("onStopEditingImpl", this, new Object[]{"", cevent, "inlineEditing"}));
    }

    public void setAllDefault() {
        getSelectedSSheet().getRowIterator().forEachRemaining(row -> row.getCellIterator().forEachRemaining(cel -> Optional.of(ObjectUtil.equal(cel.getValue(), "□符合")).filter(Boolean.TRUE::equals).ifPresent(e -> setCellValue(cel))));
        baseDao.update(device);
    }

    private void setCellValue(SCell cell) {
        String cval = cell.getStringValue();
        String cur = StrUtil.contains(cval, "□") ? "□" : "☑";
        String rep = StrUtil.contains(cval, "☑") ? "□" : "☑";
        cval = cval.replaceFirst(cur, rep);
        cell.setValue(cval);
        cellChange(cell);
        Optional.ofNullable(cell.getChooseVariable()).flatMap(choose -> Optional.ofNullable(chooseCell.get(choose))).ifPresent(lists -> {
            lists.stream().filter(cel -> ObjectUtil.equal(cel.getData(), cell.getData())).filter(cel -> !ObjectUtil.equal(cel, cell)).forEach(cel -> {
                Optional.ofNullable(cel.getValue()).map(StrUtil::toString).filter(e -> StrUtil.containsAny(e, "□", "☑")).ifPresent(e -> {
                    String ctmp = e.replaceFirst(rep, cur);
                    cel.setValue(ctmp);
                    cellChange(cel);
                });
            });
            lists.stream().filter(cel -> StrUtil.toString(cel.getValue()).contains("☑")).forEach(cel -> {
                String value = cel.getStringValue();
                String finalValue = value.replaceFirst("☑", "");
                ((JSONObject) cel.getData()).put(cel.getChooseVariable(), finalValue);
            });
        });
    }

    public void onCellClick(CellMouseEvent event) {
        SCell cell = event.getSheet().getInternalSheet().getCell(event.getRow(), event.getColumn());
        if (StrUtil.containsAny(StrUtil.toString(cell.getValue()), "□", "☑")) {
            setCellValue(cell);
            baseDao.update(device);
        } else {
            super.onCellClick(event);
        }
    }
}
