package com.ydzj.ui.base;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.sys.common.CacheUtil;
import com.sys.common.Message;
import com.sys.common.Utils;
import com.sys.core.zul.RecWnd;
import com.sys.core.zul.api.EnumType;
import com.sys.entity.ButtonCfg;
import com.sys.entity.Document;
import com.sys.entity.Maintable;
import com.ydzj.entity.YdzjConcat;
import org.zkoss.zk.ui.event.Event;
import org.zkoss.zul.CompTarget;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 0.0.1
 * @description YdzjConcatRecWnd
 * @since 2024/12/20 17:01
 */
public class YdzjConcatRecWnd extends RecWnd {
    /**
     * 处理文件上传操作
     *
     * @param event 上传触发事件
     * @throws Exception 文件上传过程中可能发生的异常
     */
    public void upload(Event event) throws Exception {
        String path = "/upload";
        JSONArray uplods = Utils.uploads(path, true, false);
        if (!uplods.isEmpty()) {
            List<Document> docs = uplods.toJavaList(Document.class);
            String saveName = docs.stream().map(e -> e.getSavename()).collect(Collectors.joining(","));
            YdzjConcat main = getMainData();
            Maintable table = CacheUtil.getMainTable(main.getClass().getName());
            if (table != null) {
                docs.stream().forEach(doc -> {
                    doc.setUpUser(Utils.getUserInfo().getUser().getUserid());
                    doc.setUpTime(Calendar.getInstance().getTime());
                    doc.setTablename(table.getTabName());
                });
                main.setFiles(main.getFiles() + "," + saveName);
                baseDao.update(main);
                baseDao.update(docs);
                //change(main, "files", main.getFiles());
                // getBaseDao().save(docs);
                // 保存文档记录后刷新附件列表窗口
                getListWnd("concat_filse").onSearch(null);
//                getBaseDao().update(main);
//                getCurrTable().reLoadData(main);

            }
        } else {
            Message.showError("上传文件错误");
        }
    }

    @Override
    public boolean save() {
        boolean save = super.save();
        detach();
        return save;
    }

    /**
     * 保存主数据并设置主联系人编号
     *
     * @return 保存操作结果（true=成功，false=失败）
     */


    @Override
    public void saveBefore(EnumType.UPDATE_TYPE type, Object data) {
        YdzjConcat ydzjConcat = (YdzjConcat) data;

        // 当合同编号为空且客户类型为"S"时，自动生成合同编号
        if (null == ydzjConcat.getConcatNo() && StrUtil.equals("S", ydzjConcat.getCustType())) {
            // 生成日期部分格式：YDZJS + 年 + 月 + 日（例如YDZJS231220）
            String datePattern = "YDZJS%tY%<tm%<td";
            // 查询当日已有合同数量
            String sql = "select  *  from ydzj_concat where concat_no like '%" + String.format(datePattern, new Date()) + "%'";
            List<Object> list = baseDao.findSql(sql);

            // 生成合同编号格式：YDZJS年月日-序号（例如YDZJS231220-01）
            // %02d 表示两位数序号，不足补零
            ydzjConcat.setConcatNo(String.format("YDZJ%tY%<tm%<td-%s",
                    new Date(),
                    String.format("%02d", list.size() + 1)));  // 序号从01开始递增
        }
        super.saveBefore(type, data);
    }

    @Override
    public boolean canChange(Object data, String field, Object orgVal, Object value) {
        String hql = "from YdzjConcat where concatNo='" + value + "'";
        YdzjConcat concat = baseDao.findOne(hql, null);
        if (StrUtil.equals(field, "concatNo")) {

            if (concat != null) {
                Message.showError("合同编号已存在");
                return false;
            }
        } else if (StrUtil.equals(field, "taxRate")) {
            YdzjConcat ydzjConcat = (YdzjConcat) data;
            double taxAmt = ydzjConcat.getNoTaxAmt() * (Double.valueOf((String) value) / 100.0);
            ydzjConcat.setTaxAmt(taxAmt);
        }
        return super.canChange(data, field, orgVal, value);
    }
}
