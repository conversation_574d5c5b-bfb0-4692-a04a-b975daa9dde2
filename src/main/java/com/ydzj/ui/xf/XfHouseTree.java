package com.ydzj.ui.xf;

import com.sys.core.zul.TopTree;
import com.ydzj.entity.YdzjOrder;
import com.ydzj.entity.YdzjXfCheckHouse;
import com.ydzj.entity.YdzjXfHouseParam;
import org.zkoss.zul.*;

import java.util.List;

public class XfHouseTree extends TopTree {
    public XfHouseTree() {
        Treecols cols = new Treecols();
        Treecol col;
        cols.setSizable(true);
        cols.appendChild(col = new Treecol("房间列表"));
        col.setStyle("background-color: #edd9c6");
        col.setIconSclass("z-icon-home");
//        A a = new A("房间列表");
//        //a.setIconSclass("z-icon-plus-square");
//        col.appendChild(a);
//        cols.setParent(this);
//        a.addEventListener("onClick", e -> {
//            YdzjOrder order = ((CompTarget) getParam("target")).getValue();
//            YdzjXfCheckHouse house = new YdzjXfCheckHouse();
//            house.setOrderId(order.getId());
//            house.setHouseNum(1);
//            baseDao.save(house);
//            addChild(null, house);
//        });
    }

    @Override
    public <T> List<T> getTreeData() {
        YdzjOrder order = ((CompTarget) getParam("target")).getValue();
        return baseDao.findHql("from YdzjXfCheckHouse where areaId = " + order.getData().getString("areaId") + " and areaId is not null");
    }


    @Override
    public boolean hasChild(Object parent) {
        return parent instanceof YdzjXfCheckHouse;
    }

    @Override
    public <T> List<T> getExpand(Object parent) {
        YdzjXfCheckHouse house = (YdzjXfCheckHouse) parent;
        return baseDao.findHql("from YdzjXfHouseParam where houseId =" + house.getId());
    }

    @Override
    public void render(Treeitem item, Object data, int val) throws Exception {
        super.render(item, data, val);
        if (item.getValue() instanceof YdzjXfCheckHouse house) {
            item.setLabel(house.getHouse());
        } else if (item.getValue() instanceof YdzjXfHouseParam param) {
            item.setLabel(param.getDeviceName());
        }
    }
}
