package com.ydzj.ui.zul;

import cn.hutool.core.util.StrUtil;
import com.sys.common.Message;
import com.sys.core.zul.ResultWnd;
import com.ydzj.entity.YdzjConcat;
import org.zkoss.zk.ui.event.Event;
import org.zkoss.zul.Listitem;
import org.zkoss.zul.Messagebox;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 0.0.1
 * @description ConcatResultWnd
 * @since 2024/12/18 14:28
 */
public class ConcatResultWnd extends ResultWnd {


    /**
     * 签订完成
     */
    public void signComplete(Event event){
        HashMap map = (HashMap) event.getData();
        Listitem listitem = (Listitem) map.get("target");
        YdzjConcat  concat = listitem.getValue();
        if (Message.showQuestion("是否确认合同签订完成？")== Messagebox.YES){
            concat.setIsConfirm("已签订");
            baseDao.update(concat);
            this.onSearch(null);
        }
    }

    /**
     * 合同完成
     * @param event
     */

    public void concatComplete(Event event){
        HashMap map = (HashMap) event.getData();
        Listitem listitem = (Listitem) map.get("target");
        YdzjConcat  concat = listitem.getValue();
        //目前已收金额
        String sql = "select sum(js_money_ss) from ydzj_js where concat_id ='"+concat.getId()+"' and js_money_ss is not null and status ='结束'";
        Object sumMoney = baseDao.getValue(sql, "sql");
        double bond = 0.0;
        if (StrUtil.contains(concat.getBond(),"%")){
            //比例
            String douBond = concat.getBond().replace("%", "");
            bond = concat.getConcatAmt() * (Double.valueOf(douBond) / 100);
        }else {
            bond =Double.valueOf(concat.getBond()==null ? "0" : concat.getBond());
        }
        double allMoney = bond + Double.valueOf(sumMoney == null ? "0" : sumMoney.toString());
        if (Message.showQuestion("当前合同已处理金额总数为："+allMoney+",是否确认完成？")== Messagebox.YES){
            concat.setIsConfirm("已完结");
            baseDao.update(concat);
            this.onSearch(null);
        }
    }

    public void jsComplete(Event event){
        HashMap map = (HashMap) event.getData();
        Listitem listitem = (Listitem) map.get("target");
        YdzjConcat  concat = listitem.getValue();
        //目前已收金额
        String sql = "select sum(js_money_ss) from ydzj_js where concat_id ='"+concat.getId()+"' and js_money_ss is not null and status ='结束'";
        Object sumMoney = baseDao.getValue(sql, "sql");
        double bond = 0.0;
        if (StrUtil.contains(concat.getBond(),"%")){
            //比例
            String douBond = concat.getBond().replace("%", "");
            bond = concat.getConcatAmt() * (Double.valueOf(douBond) / 100);
        }else {
            bond =Double.valueOf(concat.getBond()==null ? "0" : concat.getBond());
        }
        double allMoney = bond + Double.valueOf(sumMoney == null ? "0" : sumMoney.toString());
        double ysMoney = bond + concat.getConcatAmt();

        if (Message.showQuestion("当前合同应收金额:"+ysMoney+",实收金额:"+allMoney+",是否确认结算完成？")==Messagebox.YES){
            concat.setJsStatus("结算完成");
            baseDao.update(concat);
            this.onSearch(null);
        }
    }
    //检测完成
    public void detectComplete(Event event){
        HashMap map = (HashMap) event.getData();
        Listitem listitem = (Listitem) map.get("target");
        YdzjConcat  concat = listitem.getValue();
        if (Message.showQuestion("是否确定检测完成？")==Messagebox.YES){
            concat.setStatus("检测完成");
            baseDao.update(concat);
            this.onSearch(null);
        }
    }


}
