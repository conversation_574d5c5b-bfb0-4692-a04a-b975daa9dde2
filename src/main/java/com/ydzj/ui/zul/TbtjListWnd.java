package com.ydzj.ui.zul;

import cn.hutool.core.bean.BeanUtil;
import com.sys.common.Utils;
import com.sys.core.zul.ListWnd;
import com.ydzj.entity.YdzjBidding;

import java.util.HashMap;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: renzhiwei
 * @Date: 2024/12/16/15:31
 * @Description:
 */
public class TbtjListWnd extends ListWnd {
//    @Override
//    public Map<String, Object> addCondition(Map<String, Object> cond) {
//
//        if (getMainData() instanceof Object[] obj) {
//            String col = getApp().getTreeCol();
//            Utils.addCondition(cond, "eq", Utils.fieldToProperty(col), obj[0]);
//        }
//        return super.addCondition(cond);
//    }
//
//    @Override
//    public void add() throws Exception {
//        if (getMainData() instanceof Object[] obj) {
//            super.add();
//        } else {
//            Message.showError("请选择具体项目");
//        }
//    }

    @Override
    public Object formatNewObject(Object data) {
        if (getMainData() instanceof Object[] obj) {
            String col = getApp().getTreeCol();
            BeanUtil.setFieldValue(data, Utils.snakeToCamel(col), obj[0]);
        }
        return super.formatNewObject(data);
    }


    public void queryParam(){
        YdzjBidding ydzjBidding = new YdzjBidding();
        YdzjBidding bidding = Utils.openData("~./page/bidding_param.zul", ydzjBidding, this);
        if (bidding != null) {
//            Map<String,Object> param = new HashMap<>();
//            param.put("project_name",bidding.getProjectName());
//            setParam(param);
//            getApp().setMainTab("Ydzj_data_bidding");
//            getApp().setTreeCol("project_name");

        }
    }

//    @Override
//    public Maintable getMainTable() {
//        return CacheUtil.getMainTable(getApp().getMainTab());
//    }
//
//    @Override
//    public Maintable getPMainTable() {
//        return CacheUtil.getMainTable(getApp().getMainTab());
//    }


}
