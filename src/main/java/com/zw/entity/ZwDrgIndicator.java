package com.zw.entity;

import cn.hutool.core.util.StrUtil;
import lombok.Data;

import java.util.*;

import jakarta.persistence.*;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.GenericGenerator;

@Entity
@Data
@Table(name = "ZW_DRG_INDICATOR", schema = "ZW_DB")
public class ZwDrgIndicator implements java.io.Serializable {
    @Id
    @GenericGenerator(name = "ZW_DRG_INDICATOR", strategy = "assigned")
    private Long id;
    private String drgCode;
    private String deptName;
    private Double averageDay;
    private Double averageAmt;
    private Long outLy;
    private Long outZy;
    private Long outSw;
    private Long outFyz;
    private Long drgDbl;
    private Long drgZcbl;
    private Long drgGbl;
    private Double drgDifference;
    private Double amtRate;
    private Double parRate;
    private Integer zry = 0;
    private Long size = 0L;
    private Double rtRate;
    private Double zkRate;
    private Double zkOkRate;
    @Comment("出院日期")
    private java.util.Date stTime;
    @Comment("出院日期")
    private java.util.Date endTime;
    private Long btSize = 0L;
    private Double btAverageAmt;
    private Double btAverageDay;
    private Double drgCmi;
    private Double drgTcti;
    private Double drgCci;
    private Double drgMoney;
    @Comment("DRG权重")
    private Double drgWeight;
    @Comment("病组标识")
    private String drgIdentifier;
    private Double zryRate;
    private Integer okSize;
    private String inpatStateName;

    public boolean equals(Object obj) {
        return obj instanceof ZwDrgIndicator && id != null ? id.equals(((ZwDrgIndicator) obj).id) : super.equals(obj);
    }

    public int hashCode() {
        return id != null ? id.hashCode() : super.hashCode();
    }
}
