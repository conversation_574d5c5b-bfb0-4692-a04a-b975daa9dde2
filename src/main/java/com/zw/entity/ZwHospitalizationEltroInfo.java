package com.zw.entity;

import com.sys.common.SnowflakeUtil;
import com.sys.core.api.BaseEntity;
import lombok.Data;

import java.util.*;

import jakarta.persistence.*;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.GenericGenerator;

@Entity
@Data
@Table(name = "ZW_HOSPITALIZATION_ELTRO_INFO", schema = "ZW_DB")
public class ZwHospitalizationEltroInfo implements BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Comment("结算id，HIS结帐id，单次结算的唯一标识号")
    private String settId;
    @Comment("电子票据代码")
    private String billCode;
    @Comment("电子票据号码")
    private String billNo;
    @Comment("电子票据校验码")
    private String checkCode;
    @Comment("票据生成时间，格式yyyymmddhh24missff3")
    private String billTime;
    @Comment("电子票据地址，格式H5地址")
    private String billUrl;
    @Comment("电子票据二维码")
    private String billQrCode;
    @Comment("票据状态，1-开票;2-冲销或新开红票")
    private String billStatus;
    @Comment("操作类型（票据状态描述)")
    private String operationType;
    @Comment("电子票据外网地址")
    private String billNeturl;
    @Comment("病人id")
    private String pid;
    @Comment("修改时间")
    private java.util.Date updateTime;
    @Comment("创建时间")
    private java.util.Date createTime;

    public boolean equals(Object obj) {
        return obj instanceof ZwHospitalizationEltroInfo && id != null ? id.equals(((ZwHospitalizationEltroInfo) obj).id) : super.equals(obj);
    }

    public int hashCode() {
        return id != null ? id.hashCode() : super.hashCode();
    }
}
