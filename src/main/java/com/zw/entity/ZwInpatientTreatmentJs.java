package com.zw.entity;

import com.sys.core.api.BaseEntity;
import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.GenericGenerator;

import java.util.Calendar;

@Entity
@Data
@Table(name = "ZW_INPATIENT_TREATMENT_JS", schema = "ZW_DB")
public class ZwInpatientTreatmentJs implements BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Comment("结账id")
    private Long settId;
    @Comment("病人id")
    private Long pid;
    @Comment("记录性质|1(11)-预交款(冲预交),2(12)-结帐补款,前面补1,表示结帐冲款或结帐作废新产生的记录")
    private String settType;
    @Comment("结帐单据号")
    private String no;
    @Comment("结算方式，值域参见S1000服务")
    private String settMode;
    @Comment("结算金额，正数为结帐补款，负数为退款")
    private Double settPrice;
    @Comment("交易流水号")
    private String settSeNum;
    @Comment("结帐时间|yyyy-mm-dd hh24:mi:ss")
    private String oprtrTime;
    @Comment("操作员姓名")
    private String oprtrName;
    @Comment("创建时间")
    private java.util.Date createTime = Calendar.getInstance().getTime();
    @Comment("修改时间")
    private java.util.Date updateTime = Calendar.getInstance().getTime();

    public boolean equals(Object obj) {
        return obj instanceof ZwInpatientTreatmentJs && id != null ? id.equals(((ZwInpatientTreatmentJs) obj).id) : super.equals(obj);
    }

    public int hashCode() {
        return id != null ? id.hashCode() : super.hashCode();
    }
}
