package com.zw.entity;

import com.sys.core.api.BaseEntity;
import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.Comment;

import java.util.Calendar;

@Entity
@Data
@Table(name = "ZW_SD_CODE_DICTIONARY", schema = "ZW_DB")
public class ZwSdCodeDictionary implements BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Comment("手术操作代码")
    private String surgCode;
    @Comment("手术操作名称")
    private String surgName;
    @Comment("手术等级")
    private String surgLevel;
    @Comment("创建时间,格式yyyy-mm-dd hh24:mi:ss")
    private java.util.Date createDate;
    @Comment("拼音码")
    private String cadnBecodePy;
    @Comment("五笔码")
    private String cadnBecodeWb;
    @Comment("手术类别编码")
    private String surgTypeCode;
    @Comment("手术类别名称")
    private String surgTypeName;
    @Comment("ICD编码")
    private String icdCode;
    @Comment("最后更新时间,格式yyyymm-dd hh24:mi:ss")
    private java.util.Date modifyDate;
    @Comment("最后操作类型")
    private java.util.Date lastOperTyp;
    @Comment("创建时间")
    private java.util.Date createTime = Calendar.getInstance().getTime();
    @Comment("修改时间")
    private java.util.Date updateTime = Calendar.getInstance().getTime();

    public boolean equals(Object obj) {
        return obj instanceof ZwSdCodeDictionary && id != null ? id.equals(((ZwSdCodeDictionary) obj).id) : super.equals(obj);
    }

    public int hashCode() {
        return id != null ? id.hashCode() : super.hashCode();
    }
}
