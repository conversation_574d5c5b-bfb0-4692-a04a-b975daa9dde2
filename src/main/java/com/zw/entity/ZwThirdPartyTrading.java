package com.zw.entity;
import com.sys.core.api.BaseEntity;
import lombok.Data;
import java.util.*;
import jakarta.persistence.*;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.GenericGenerator;
@Entity
@Data
@Table(name = "ZW_THIRD_PARTY_TRADING")
public class ZwThirdPartyTrading implements BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Comment("单据类型|1-预交款;2-住院结帐;3-门诊收费;4-挂号;11-冲预交")
    private String feeType;
    @Comment("单据号（为预交单据号或结帐单据号）")
    private String receiptNo;
    @Comment("病人id")
    private String pid;
    @Comment("结算方式：医保基金，个人账户，微信自助扫码付")
    private String payMode;
    @Comment("支付交易流水号")
    private String payNo;
    @Comment("收款时间，格式yyyy-mm-dd hh24:mi:ss")
    private String recpTime;
    @Comment("金额")
    private Double actualMoney;
    @Comment("结帐id")
    private String settId;
    @Comment("卡类别id")
    private String cardTypeId;
    @Comment("合作单位名称")
    private String resourceHzdw;
    @Comment("单据状态|1-正常收费;2-退费单据;3-被退费单据（原始）")
    private String chrgStatus;
    @Comment("预交id")
    private String yjId;
    @Comment("患者姓名")
    private String patName;
    @Comment("就诊号|门诊号或住院号")
    private String patVisitNo;
    @Comment("操作员姓名(设备名称)")
    private String oprtrName;
    @Comment("修改时间")
    private java.util.Date updateTime=Calendar.getInstance().getTime();
    @Comment("创建时间")
    private java.util.Date createTime=Calendar.getInstance().getTime();
    public boolean equals(Object obj) {
        return obj instanceof ZwThirdPartyTrading && id != null ? id.equals(((ZwThirdPartyTrading) obj).id) : super.equals(obj);
    }
    public int hashCode() {
        return id != null?id.hashCode():super.hashCode();
    }
}
