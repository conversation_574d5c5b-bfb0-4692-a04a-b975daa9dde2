package com.zw.entity;
import com.sys.core.api.BaseEntity;
import lombok.Data;
import java.util.*;
import jakarta.persistence.*;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.GenericGenerator;
@Entity
@Data
@Table(name = "ZW_THIRD_PARTY_TRADING_SUB")
public class ZwThirdPartyTradingSub implements BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    @Comment("三方交易对账id（关联zw_third_party_trading id）")
    private Long partyTradingId;
    @Comment("扩展信息名称")
    private String tranName;
    @Comment("扩展信息内容")
    private String tranCont;
    @Comment("修改时间")
    private java.util.Date updateTime=Calendar.getInstance().getTime();
    @Comment("创建时间")
    private java.util.Date createTime=Calendar.getInstance().getTime();
    public boolean equals(Object obj) {
        return obj instanceof ZwThirdPartyTradingSub && id != null ? id.equals(((ZwThirdPartyTradingSub) obj).id) : super.equals(obj);
    }
    public int hashCode() {
        return id != null?id.hashCode():super.hashCode();
    }
}
