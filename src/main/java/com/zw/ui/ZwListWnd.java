package com.zw.ui;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSONObject;
import com.sys.common.CacheUtil;
import com.sys.common.Utils;
import com.sys.core.zul.ListWnd;
import com.sys.entity.Appconfig;
import com.sys.entity.ButtonCfg;
import com.sys.entity.TableInfo;
import com.zw.entity.ZwDrgAverage;
import com.zw.entity.ZwPatientUserDetails;
import com.zw.entity.home.ZwHomePatientInfo;
import org.apache.poi.ss.formula.functions.T;
import org.zkoss.zk.ui.Executions;
import org.zkoss.zk.ui.event.Event;
import org.zkoss.zul.*;

import java.io.ByteArrayOutputStream;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

public class ZwListWnd extends ListWnd {
    @Override
    public boolean checkBtncfg(Object data, ButtonCfg cfg) {
        ZwPatientUserDetails details = (ZwPatientUserDetails) data;
        if (StrUtil.equals(cfg.getMethodName(), "viewDrg")) {
            return StrUtil.isNotBlank(details.getDrgCode());
        }
        return super.checkBtncfg(data, cfg);
    }

    public void viewDrg(Event event) {
        Map<String, Object> param = (Map<String, Object>) event.getData();
        CompTarget target = (CompTarget) param.get("target");
        ZwPatientUserDetails x = target.getValue();
        ZwHomePatientInfo patientInfo = baseDao.find(ZwHomePatientInfo.class, x.getPid());
        JSONObject group = patientInfo.getDrgGroupInfo();
        Date endDate = ObjectUtil.defaultIfNull(x.getEndTime(), DateUtil.date());
        long day = DateUtil.between(x.getAdtaTime(), endDate, DateUnit.DAY);
        ButtonCfg cfg = (ButtonCfg) param.get("cfg");
        if (StrUtil.equals(cfg.getDispName(), "opter")) {
            Window window = (Window) Executions.createComponents("~./page/all_drg_info.zul", null, param);
            Tabs tabs = (Tabs) window.getFellow("tabs");
            Tabpanels tabpanels = (Tabpanels) window.getFellow("tabpanels");
            List<ZwDrgAverage> averageList = baseDao.findHql("from ZwDrgAverage");
            Map<String, ZwDrgAverage> averageMap = averageList.stream().collect(Collectors.toMap(e -> e.getDeptName() + ">" + e.getDrgCode(), e -> e, (k1, k2) -> k1));

            group.forEach((k, v) -> {
                ZwDrgAverage average = averageMap.get(x.getDeptName() + ">" + k);
                String averageAmt = null != average ? String.valueOf(average.getAverageAmt()) : "";
                String averageDay = null != average ? String.valueOf(average.getAverageDay()) : "";
                Object allMoney = ObjectUtil.defaultIfNull(x.getNobalanceMoney(), 1000);
                String groupVal = String.format("%s,%s,%s,,,,%s,1,\"%s\",\"%s\"", x.getInpno(), StrUtil.equals(x.getPatSex(), "男") ? 1 : 2, x.getPatAge().replaceAll("岁", ""), day, ((String) v).replaceAll("\\+", "%2B"), StrUtil.nullToDefault(x.getSsCode(), "").replaceAll("\\+", "%2B"));
                String url = String.format("/drg/index.html?type=chongqing_2024&groupVal=%s&allMoney=%s&averageAmt=%s&averageDay=%s", groupVal, allMoney, averageAmt, averageDay);
                tabs.appendChild(new Tab(k));
                Tabpanel tabpanel = new Tabpanel();
                tabpanels.appendChild(tabpanel);
                Iframe iframe = new Iframe();
                tabpanel.appendChild(iframe);
                iframe.setSrc(url);
                iframe.setHflex("1");
                iframe.setVflex("1");
            });
            window.doModal();
        } else {

            String zdStr = x.getZdCode();
            ZwDrgAverage average = baseDao.findOne("from ZwDrgAverage where id='" + zdStr + "'", null);
            String averageAmt = null != average ? String.valueOf(average.getAverageAmt()) : "";
            String averageDay = null != average ? String.valueOf(average.getAverageDay()) : "";

            String groupVal = String.format("%s,%s,%s,,,,%s,1,\"%s\",\"%s\"", x.getInpno(), StrUtil.equals(x.getPatSex(), "男") ? 1 : 2, x.getPatAge().replaceAll("岁", ""), day, zdStr.replaceAll("\\+", "%2B"), StrUtil.nullToDefault(x.getSsCode(), "").replaceAll("\\+", "%2B"));
            String url = String.format("/drg/index.html?type=chongqing_2024&groupVal=%s&allMoney=%s&averageAmt=%s&averageDay=%s", groupVal, ObjectUtil.defaultIfNull(x.getNobalanceMoney(), 1000), averageAmt, averageDay);
            Window window = (Window) Executions.createComponents("~./page/drg_info.zul", null, param);
            Iframe iframe = (Iframe) window.getFellow("iframe");
            iframe.setSrc(url);
            window.doModal();
        }
    }

    public void export() throws Exception {
        Class<?> dbClass = getDbClass();
        if (dbClass == null || baseDao == null || getMainTable() == null) {
            return;
        }
        List<T> data = baseDao.find(dbClass, -1, -1, getCond());
        try {
            ExcelWriter bigWriter = ExcelUtil.getBigWriter();
            bigWriter.setOnlyAlias(true);
            AtomicInteger colIndex = new AtomicInteger();
            List<TableInfo> tableInfo = CacheUtil.getTableInfo(getMainTable().getTabName());
            getExportCfgList().stream().filter(cfg -> tableInfo.stream().anyMatch(info -> StrUtil.equals(info.getDispname(), cfg.getDispName()))).forEach(cfg -> {
                bigWriter.addHeaderAlias(cfg.getDispName(), cfg.getCfgDes());
                bigWriter.setColumnWidth(colIndex.getAndIncrement(), 20);
            });
            bigWriter.write(data, true);
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            bigWriter.flush(outputStream, true);
            bigWriter.close();
            String fileName = getMainTable().getTabDes() + ".xlsx";
            Filedownload.save(outputStream.toByteArray(), "application/octet-stream", fileName);
            IoUtil.close(outputStream);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public List<Appconfig> getExportCfgList() {
        List<Appconfig> cfgList = ObjectUtil.cloneByStream(getCfgList());
        cfgList.removeIf(cfg -> cfg.getDispName().equals("idno"));
        return cfgList;
    }

    public Map<String, Object> addCondition(Map<String, Object> cond) {
        if (getUserInfo().getGrpList().contains("0004")) {
            Utils.addCondition(cond, "eq", "deptId", getUser().getDeptId());
        }
        return super.addCondition(cond);
    }
}
