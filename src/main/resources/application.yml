#用户必须要有权限
binlog:
  # 监听数据库,如填写监听库下面的所有表,如只是单独的表只填写dbTable,不需要监听则需要ignoreTable,synData开启则同步到从库中
  #系统默认同步s_document表下面的文件，enable是开启binlog服务,如不需要则不用
  host: localhost
  port: 3306
  username: root
  password: root
  # 监听数据库格式[库,库]
  database: test
  # 监听数据库与表,隔开，格式[库.表,,,]
  dbTable: test.s_document
  ignoreTable: s_user_log,s_logs
  serverId: 1
  enable: false
  synData: true
debug: false
#Ocr识别库
tess4j:
  datapath: /usr/local/share/tessdata/
logging:
  config: classpath:logback-spring.xml
spring:
  data:
    redis:
      host: **************
      port: 6379
      password: redis123!
      database: 0
  profiles:
    active: test
  jpa:
    show-sql: true
    #    database: mysql
    hibernate:
      ddl-auto: none
    open-in-view: false
    database-platform: org.hibernate.dialect.MariaDB106Dialect
    properties:
      hibernate.jdbc.batch_size: 200
      hibernate.order_inserts: true
      hibernate.order_updates: true
      hibernate.flush.mode: COMMIT
      hibernate.flush.generate_statistics: true
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: UTC
  freemarker:
    charset: utf-8
    check-template-location: false
#系统基本配置
base:
  showCfg: true
  userLog: false
  dialect: MySQL5Dialec
  upload: upload
  platform: ydzj
  ignore: phone,auth,login,drg,syn
  sm4Key: OhepchW0EWcH3iet0wHbcg==
  check: /static/flow/config/,/files/report/
  IPWhite: *************
  interceptor: /auth
  platformName: 病案质控DRG管理平台
  chromedriver: C:/yzjw/chromedriver/chromedriver.exe
