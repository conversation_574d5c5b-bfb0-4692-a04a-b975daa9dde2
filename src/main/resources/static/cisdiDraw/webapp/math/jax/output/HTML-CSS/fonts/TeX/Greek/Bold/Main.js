/*
 *  /MathJax/jax/output/HTML-CSS/fonts/TeX/Greek/Bold/Main.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS["MathJax_Greek-bold"]={directory:"Greek/Bold",family:"MathJax_Greek",weight:"bold",testString:"\u0393 \u03A5 \u039B",32:[0,0,250,0,0],160:[0,0,250,0,0],915:[680,0,692,39,643],916:[698,0,958,56,901],920:[696,10,894,64,829],923:[698,0,806,40,765],926:[675,0,767,48,718],928:[680,0,900,39,860],931:[686,0,831,64,766],933:[697,0,894,64,829],934:[686,0,831,64,766],936:[686,0,894,64,829],937:[696,1,831,51,779]};MathJax.Callback.Queue(["initFont",MathJax.OutputJax["HTML-CSS"],"MathJax_Greek-bold"],["loadComplete",MathJax.Ajax,MathJax.OutputJax["HTML-CSS"].fontDir+"/Greek/Bold/Main.js"]);
