/*
 *  /MathJax/jax/output/HTML-CSS/fonts/TeX/Typewriter/Regular/BasicLatin.js
 *
 *  Copyright (c) 2009-2018 The MathJax Consortium
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

MathJax.Hub.Insert(MathJax.OutputJax["HTML-CSS"].FONTDATA.FONTS.MathJax_Typewriter,{32:[0,0,250,0,0],33:[622,0,525,206,320],34:[623,-333,525,122,402],35:[611,0,525,36,489],36:[694,82,525,58,466],37:[694,83,525,35,489],38:[622,11,525,28,490],39:[611,-287,525,175,349],40:[694,82,525,166,437],41:[694,82,525,87,358],42:[520,-90,525,68,456],43:[531,-81,525,38,487],44:[140,139,525,173,353],45:[341,-271,525,57,468],46:[140,-1,525,193,332],47:[694,83,525,58,466],48:[621,10,525,42,482],49:[622,-1,525,99,450],50:[622,-1,525,52,472],51:[622,11,525,44,479],52:[624,-1,525,29,495],53:[611,10,525,52,472],54:[622,11,525,45,479],55:[627,10,525,44,480],56:[621,10,525,45,479],57:[622,11,525,46,479],58:[431,-1,525,193,332],59:[431,139,525,175,337],60:[557,-55,525,57,468],61:[417,-195,525,38,487],62:[557,-55,525,57,468],63:[617,0,525,62,462],64:[617,6,525,44,481],65:[623,-1,525,28,496],66:[611,-1,525,17,482],67:[622,11,525,40,484],68:[611,-1,525,16,485],69:[611,-1,525,19,502],70:[611,-1,525,22,490],71:[622,11,525,38,496],72:[611,-1,525,16,508],73:[611,-1,525,72,452],74:[611,11,525,57,479],75:[611,-1,525,18,495],76:[611,0,525,25,488],77:[611,-1,525,12,512],78:[611,0,525,20,504],79:[621,10,525,56,468],80:[611,-1,525,19,480],81:[621,138,525,56,468],82:[611,11,525,16,522],83:[622,11,525,52,472],84:[611,-1,525,26,498],85:[611,11,525,-3,528],86:[611,7,525,19,505],87:[611,7,525,12,512],88:[611,-1,525,28,495],89:[611,-1,525,20,505],90:[611,-1,525,48,481],91:[694,82,525,214,483],92:[694,83,525,58,466],93:[694,82,525,41,310],94:[611,-460,525,96,428],95:[-25,95,525,57,468],96:[681,-357,525,176,350],97:[439,6,525,48,524],98:[611,6,525,4,492],99:[440,6,525,66,466],100:[611,6,525,31,520],101:[440,6,525,48,464],102:[617,-1,525,35,437],103:[442,229,525,28,509],104:[611,-1,525,4,520],105:[612,-1,525,72,462],106:[612,228,525,48,376],107:[611,-1,525,13,507],108:[611,-1,525,51,474],109:[436,-1,525,-12,536],110:[436,-1,525,4,520],111:[440,6,525,52,472],112:[437,221,525,4,492],113:[437,221,525,34,545],114:[437,-1,525,24,487],115:[440,6,525,72,458],116:[554,6,525,25,448],117:[431,5,525,4,520],118:[431,4,525,24,500],119:[431,4,525,16,508],120:[431,-1,525,29,495],121:[431,228,525,26,500],122:[431,-1,525,34,475],123:[694,83,525,50,475],124:[694,82,525,228,297],125:[694,83,525,49,475],126:[611,-466,525,87,437],127:[612,-519,525,104,421]});MathJax.Ajax.loadComplete(MathJax.OutputJax["HTML-CSS"].fontDir+"/Typewriter/Regular/BasicLatin.js");
