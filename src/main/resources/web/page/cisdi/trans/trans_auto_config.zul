<childwnd use="com.cisdi.ui.TransChildWnd" id="chd"  item2top="false" vflex="1"
          addToolBar='[{"method":"groupCab","name":"生成组柜信息","icon":"z-icon-plus-square"},{"method":"autoConfig","name":"自动组柜"},{"method":"interFace","name":"生成接线","icon":"z-icon-plus-square"}
          ,{"method":"clearAutoConfig","name":"清除组柜","icon":"z-icon-plus-square"},{"method":"submit","name":"下一步","icon":"z-icon-plus-square"}]'>
    <vlayout vflex="1" hflex="1">
        <iframe id="iframe" hflex="1" vflex="1" name="iframe"/>
    </vlayout>

    <script type="text/javascript">
        <![CDATA[
		function autoConfig(type){
			var iframe=document.getElementsByName("iframe");
	 		var ifrmWindow =iframe[0].contentWindow;
	 		if(type=='config'){
	 			ifrmWindow.z_oneKey();
	 		}else if(type=='eplan'){
	 			ifrmWindow.z_eplanCab();
	 		}else if(type=='dqCab'){
	 			ifrmWindow.dqCab();
	 		}else{
	 			ifrmWindow.z_erroMsg();
	 		}
	 	}
		function saveEplan(){
			var recWnd=zk.Widget.$(jq('$chd'));
			var iframe=document.getElementsByName("iframe");
		 	var ifrmWindow =iframe[0].contentWindow;
		 	var data={"xml":ifrmWindow.z_save(),"data":ifrmWindow.eplanData,"eplanData":ifrmWindow.eplanData};
		 	zAu.send(new zk.Event(recWnd,"onSaveEplan",data));
	 }
		function saveXml(jsonStr){
			var recWnd=zk.Widget.$(jq('$chd'));
			var iframe=document.getElementsByName("iframe");
		 	var ifrmWindow =iframe[0].contentWindow;
		 	ifrmWindow.cabCount=jsonStr;
		 	zAu.send(new zk.Event(recWnd,"onEplanXml",{"xml":ifrmWindow.z_save(),"cells":ifrmWindow.cells,"updateCab":ifrmWindow.updateCabNumber}));
         	zAu.send(new zk.Event(recWnd,"onEplanData",{"eplanData":ifrmWindow.eplanData,"cells":ifrmWindow.cells,"fengbi":ifrmWindow._generatrix}));

		 }
		 function addEqu(data){
		    var recWnd=zk.Widget.$(jq('$chd'));
			var iframe=document.getElementsByName("iframe");
		 	var ifrmWindow =iframe[0].contentWindow;
		 	ifrmWindow.addEqu(data);
		 }
		 function editEqu(cab_code,type){
            var recWnd=zk.Widget.$(jq('$chd'));
            zAu.send(new zk.Event(recWnd,"editEqu",{"cab_code":cab_code,"type":type}));
		 }
		 window.editEqu=editEqu;
		  function editScheme(inter_id){
            var recWnd=zk.Widget.$(jq('$chd'));
            var editScheme = window.curEditCel.value;
            zAu.send(new zk.Event(recWnd,"editScheme",{"inter_id":inter_id,"editScheme":editScheme}));
		 }

		   function updateCab(cab_code){
            var recWnd=zk.Widget.$(jq('$chd'));
            zAu.send(new zk.Event(recWnd,"updateCab",{"cab_code":cab_code}));
		 }
		 window.updateCab=updateCab;
		function readXml(url){
			var recWnd=zk.Widget.$(jq('$chd'));
			var iframe=document.getElementsByName("iframe");
		 	var ifrmWindow =iframe[0].contentWindow;
		 	ifrmWindow.eplanXml=null;
			jq("$EplanXml").load(url,
					function(data) {
						ifrmWindow.eplanXml=data;
					})
				}
		]]>
	</script>
</childwnd>
