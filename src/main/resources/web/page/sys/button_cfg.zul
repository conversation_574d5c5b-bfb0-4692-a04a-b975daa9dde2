<recwnd width="650px" toolItems="save" use="com.sys.ui.ButtonCfgRecWnd" id="btnWnd">
    <grid>
        <columns>
            <column width="70px"/>
            <column hflex="5"/>
        </columns>
        <rows>
            <row>
                <label value="方法名"/>
                <textbox bind="s_button_cfg.methodName" require="true"/>
            </row>
            <row>
                <label value="描述"/>
                <textbox bind="s_button_cfg.methodDesc"/>
            </row>
            <row>
                <label value="配置类型"/>
                <combobox require="true" bind="s_button_cfg.cfgtype">
                    <comboitem label="Button"/>
                    <comboitem label="String"/>
                </combobox>
            </row>

            <row>
                <label value="图标"/>
                <bandbox bind="s_button_cfg.image" lookup="icon" hflex="1"/>
            </row>
            <row>
                <label value="排序"/>
                <longbox bind="s_button_cfg.sortId"/>

            </row>
            <row>
                <label value="显示宽度"/>
                <textbox bind="s_button_cfg.width"/>
            </row>
            <row >
                <checkbox bind="s_button_cfg.isfile" label="文件"/>
                <hlayout hflex="1">
                    <textbox bind="s_button_cfg.fileCol" id="fileCol" hflex="1"/>
                    <checkbox bind="s_button_cfg.openWnd" label="新窗口"/>
                    <checkbox bind="s_button_cfg.report" label="报表"/>
                </hlayout>
            </row>
            <row>
                <label value="上传路径"/>
                <textbox bind="s_button_cfg.filePath"/>
            </row>
            <row>
                <label value="权限组"/>
                <bandbox bind="s_button_cfg.grpNum" multiple="true" lookup="app_grpnum"/>
            </row>
            <row>
                <label value="显示条件"/>
                <hlayout hflex="1">
                    <textbox bind="s_button_cfg.condCol"/>
                    <label value="包含" width="40px"/>
                    <textbox bind="s_button_cfg.condVal"/>
                    <button label="添加" onClick="btnWnd.addCond()"/>
                </hlayout>
            </row>
            <row spans="2">
                <textbox bind="s_button_cfg.condJson" height="60px" multiline="true"/>
            </row>
        </rows>
    </grid>
</recwnd>
