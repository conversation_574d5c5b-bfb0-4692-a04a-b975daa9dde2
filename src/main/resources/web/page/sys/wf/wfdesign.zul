<recwnd vflex="1" hflex="1" use="com.sys.ui.wf.DesignWnd" id="chd">
    <grid>
        <columns>
            <column width="80px"/>
            <column/>
            <column width="70px"/>
            <column width="50px"/>
        </columns>
        <rows>
            <row>
                <label value="流程名称"/>
                <textbox bind="s_wf_file.wfname"/>
                <label value="当前版本"/>
                <intbox bind="s_wf_file.version" readonly="true"/>
            </row>
            <row spans="4" style="display: none;">
                <textbox bind="s_wf_file.wfcont" id="xml"/>
            </row>
        </rows>
    </grid>
    <iframe src="~./static/flow/design.html" vflex="1" hflex="1" id="iframe" name="iframe"/>

    <script >
        <![CDATA[
		function saveXml(){
	    	var recWnd=zk.Widget.$(jq('$chd'));
			var iframe=document.getElementsByName("iframe");
		 	var ifrmWindow =iframe[0].contentWindow;
		 	var xml=ifrmWindow.flowData();
		 	zAu.send(new zk.Event(recWnd,"onSaveXml",xml));
		 	}
		 	function loadXml(){
		 	var xml=zk.Widget.$(jq('$xml')).getValue();
			var iframe=document.getElementsByName("iframe");
		 	var ifrmWindow =iframe[0].contentWindow;
		 	ifrmWindow.loadXml(xml);
		 	}
		 	function develop(){
		 	  var recWnd=zk.Widget.$(jq('$chd'));
		 	  var xml=zk.Widget.$(jq('$xml')).getValue();
			  var iframe=document.getElementsByName("iframe");
		 	  var ifrmWindow =iframe[0].contentWindow;
		 	  var cells=ifrmWindow.getCells(xml);
		 	  zAu.send(new zk.Event(recWnd,"onDevelop",cells));
		 	}
		]]>
    </script>

</recwnd>
